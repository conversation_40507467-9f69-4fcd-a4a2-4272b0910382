syntax = "proto3";

package com.mengchong.xiaoxiaole.protobuf;

// 游戏消息类型枚举
enum MessageType {
    LOGIN_REQUEST = 0;       // 登录请求
    LOGIN_RESPONSE = 1;      // 登录响应
    GAME_START = 2;          // 游戏开始
    GAME_MOVE = 3;           // 玩家移动
    MATCH_RESULT = 4;        // 匹配结果
    SCORE_UPDATE = 5;        // 分数更新
    GAME_OVER = 6;           // 游戏结束
    ERROR_MESSAGE = 7;       // 错误消息
    HEARTBEAT = 8;           // 心跳包
}

// 登录请求
message LoginRequest {
    string player_id = 1;    // 玩家ID
    string nickname = 2;     // 玩家昵称
    string avatar = 3;       // 玩家头像URL
    string device_id = 4;    // 设备ID
}

// 登录响应
message LoginResponse {
    bool success = 1;        // 是否成功
    string message = 2;      // 消息
    int32 player_level = 3;  // 玩家等级
    int32 coin_count = 4;    // 金币数量
}

// 游戏移动请求
message GameMoveRequest {
    int32 from_x = 1;        // 起始X坐标
    int32 from_y = 2;        // 起始Y坐标
    int32 to_x = 3;          // 目标X坐标
    int32 to_y = 4;          // 目标Y坐标
}

// 匹配结果
message MatchResult {
    bool valid = 1;          // 是否有效匹配
    repeated MatchItem matches = 2;  // 匹配项列表
    int32 score = 3;         // 本次匹配得分
    bool level_up = 4;       // 是否升级
}

// 匹配项
message MatchItem {
    int32 type = 1;          // 动物类型
    repeated Position positions = 2;  // 匹配位置列表
}

// 位置坐标
message Position {
    int32 x = 1;             // X坐标
    int32 y = 2;             // Y坐标
}

// 分数更新
message ScoreUpdate {
    int32 current_score = 1; // 当前分数
    int32 target_score = 2;  // 目标分数
    int32 combo_count = 3;   // 连击数
}

// 游戏结束
message GameOver {
    bool is_win = 1;         // 是否胜利
    int32 final_score = 2;   // 最终分数
    int32 rank = 3;          // 排名
    int32 reward = 4;        // 奖励金币
}

// 错误消息
message ErrorMessage {
    int32 code = 1;          // 错误码
    string description = 2;  // 错误描述
}

// 统一游戏消息结构
message GameMessage {
    MessageType type = 1;    // 消息类型
    int64 timestamp = 2;     // 时间戳
    string request_id = 3;   // 请求ID
    oneof data {
        LoginRequest login_request = 4;
        LoginResponse login_response = 5;
        GameMoveRequest game_move = 6;
        MatchResult match_result = 7;
        ScoreUpdate score_update = 8;
        GameOver game_over = 9;
        ErrorMessage error = 10;
    }
}