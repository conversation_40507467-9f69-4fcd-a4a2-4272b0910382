package com.mengchong.xiaoxiaole;

import com.mengchong.xiaoxiaole.server.MengchongXiaoxiaoleServer;
import com.mengchong.xiaoxiaole.server.config.NettyServerProperties;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

import jakarta.annotation.PreDestroy;

@SpringBootApplication
@ComponentScan(basePackages = "com.mengchong.xiaoxiaole")
@EnableConfigurationProperties(NettyServerProperties.class)
public class MengchongXiaoxiaoleApplication implements CommandLineRunner {

    private final MengchongXiaoxiaoleServer nettyServer;
    private final NettyServerProperties serverProperties;

    public MengchongXiaoxiaoleApplication(NettyServerProperties serverProperties) {
        this.serverProperties = serverProperties;
        this.nettyServer = new MengchongXiaoxiaoleServer(serverProperties.getPort());
    }

    public static void main(String[] args) {
        SpringApplication.run(MengchongXiaoxiaoleApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("启动萌宠消消乐WebSocket服务...");
        System.out.println("服务端口: " + serverProperties.getPort());
        System.out.println("WebSocket路径: " + serverProperties.getWebsocket().getPath());
        new Thread(() -> {
            try {
                nettyServer.start();
            } catch (Exception e) {
                System.err.println("Netty服务启动失败: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }

    @PreDestroy
    public void stop() {
        System.out.println("停止萌宠消消乐WebSocket服务...");
        // TODO: 实现服务优雅关闭逻辑
    }
}