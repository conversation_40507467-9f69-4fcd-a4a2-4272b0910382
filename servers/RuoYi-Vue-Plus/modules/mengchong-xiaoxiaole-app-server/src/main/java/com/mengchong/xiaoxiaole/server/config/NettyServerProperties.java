package com.mengchong.xiaoxiaole.server.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "server")
public class NettyServerProperties {
    private int port = 8088;
    private Netty netty = new Netty();
    private Websocket websocket = new Websocket();

    public static class Netty {
        private int bossGroupThreads = 1;
        private int workerGroupThreads = 4;
        private int soBacklog = 1024;
        private boolean soKeepAlive = true;
        private boolean tcpNodelay = true;
        private int connectTimeout = 30000;
        private int writeBufferHighWaterMark = 65536;
        private int writeBufferLowWaterMark = 32768;

        // Getters and Setters
        public int getBossGroupThreads() { return bossGroupThreads; }
        public void setBossGroupThreads(int bossGroupThreads) { this.bossGroupThreads = bossGroupThreads; }
        public int getWorkerGroupThreads() { return workerGroupThreads; }
        public void setWorkerGroupThreads(int workerGroupThreads) { this.workerGroupThreads = workerGroupThreads; }
        public int getSoBacklog() { return soBacklog; }
        public void setSoBacklog(int soBacklog) { this.soBacklog = soBacklog; }
        public boolean isSoKeepAlive() { return soKeepAlive; }
        public void setSoKeepAlive(boolean soKeepAlive) { this.soKeepAlive = soKeepAlive; }
        public boolean isTcpNodelay() { return tcpNodelay; }
        public void setTcpNodelay(boolean tcpNodelay) { this.tcpNodelay = tcpNodelay; }
        public int getConnectTimeout() { return connectTimeout; }
        public void setConnectTimeout(int connectTimeout) { this.connectTimeout = connectTimeout; }
        public int getWriteBufferHighWaterMark() { return writeBufferHighWaterMark; }
        public void setWriteBufferHighWaterMark(int writeBufferHighWaterMark) { this.writeBufferHighWaterMark = writeBufferHighWaterMark; }
        public int getWriteBufferLowWaterMark() { return writeBufferLowWaterMark; }
        public void setWriteBufferLowWaterMark(int writeBufferLowWaterMark) { this.writeBufferLowWaterMark = writeBufferLowWaterMark; }
    }

    public static class Websocket {
        private String path = "/game";
        private int maxFramePayloadLength = 655360;
        private int idleTimeout = 300;
        private int heartBeatInterval = 30;

        // Getters and Setters
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        public int getMaxFramePayloadLength() { return maxFramePayloadLength; }
        public void setMaxFramePayloadLength(int maxFramePayloadLength) { this.maxFramePayloadLength = maxFramePayloadLength; }
        public int getIdleTimeout() { return idleTimeout; }
        public void setIdleTimeout(int idleTimeout) { this.idleTimeout = idleTimeout; }
        public int getHeartBeatInterval() { return heartBeatInterval; }
        public void setHeartBeatInterval(int heartBeatInterval) { this.heartBeatInterval = heartBeatInterval; }
    }

    // Getters and Setters
    public int getPort() { return port; }
    public void setPort(int port) { this.port = port; }
    public Netty getNetty() { return netty; }
    public void setNetty(Netty netty) { this.netty = netty; }
    public Websocket getWebsocket() { return websocket; }
    public void setWebsocket(Websocket websocket) { this.websocket = websocket; }
}