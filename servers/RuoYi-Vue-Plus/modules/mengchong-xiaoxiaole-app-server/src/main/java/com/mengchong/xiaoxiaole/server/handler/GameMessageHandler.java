package com.mengchong.xiaoxiaole.server.handler;

import com.google.protobuf.InvalidProtocolBufferException;
import com.mengchong.xiaoxiaole.protobuf.GameMessage;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;

public class GameMessageHandler extends SimpleChannelInboundHandler<GameMessage> {
    private static final Logger logger = LoggerFactory.getLogger(GameMessageHandler.class);
    // 存储在线玩家连接 (playerId -> ChannelHandlerContext)
    private static final ConcurrentHashMap<String, ChannelHandlerContext> onlinePlayers = new ConcurrentHashMap<>();

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, GameMessage msg) throws Exception {
        logger.info("收到游戏消息: 类型={}, 时间戳={}", msg.getType(), msg.getTimestamp());

        switch (msg.getType()) {
            case LOGIN_REQUEST:
                handleLoginRequest(ctx, msg.getLoginRequest());
                break;
            case GAME_MOVE:
                handleGameMove(ctx, msg.getGameMove());
                break;
            case HEARTBEAT:
                handleHeartbeat(ctx, msg);
                break;
            case GAME_OVER:
                handleGameOver(ctx, msg.getGameOver());
                break;
            default:
                logger.warn("未处理的消息类型: {}", msg.getType());
                sendErrorMessage(ctx, 400, "不支持的消息类型: " + msg.getType());
        }
    }

    /**
     * 处理登录请求
     */
    private void handleLoginRequest(ChannelHandlerContext ctx, GameMessage.LoginRequest request) {
        String playerId = request.getPlayerId();
        logger.info("玩家登录: playerId={}, nickname={}", playerId, request.getNickname());

        // 将玩家连接存入在线列表
        onlinePlayers.put(playerId, ctx);

        // 构建登录响应
        GameMessage.LoginResponse response = GameMessage.LoginResponse.newBuilder()
                .setSuccess(true)
                .setMessage("登录成功")
                .setPlayerLevel(1)
                .setCoinCount(1000)
                .build();

        // 发送响应
        sendMessage(ctx, GameMessage.MessageType.LOGIN_RESPONSE, response);
    }

    /**
     * 处理游戏移动请求
     */
    private void handleGameMove(ChannelHandlerContext ctx, GameMessage.GameMoveRequest move) {
        logger.info("玩家移动: from=({},{}), to=({},@{})", 
                move.getFromX(), move.getFromY(), move.getToX(), move.getToY());

        // TODO: 实现游戏逻辑处理
        // 1. 验证移动是否有效
        // 2. 检测匹配结果
        // 3. 更新游戏状态
        // 4. 发送匹配结果给客户端
    }

    /**
     * 处理心跳包
     */
    private void handleHeartbeat(ChannelHandlerContext ctx, GameMessage msg) {
        // 回应心跳包
        sendMessage(ctx, GameMessage.MessageType.HEARTBEAT, null);
    }

    /**
     * 处理游戏结束
     */
    private void handleGameOver(ChannelHandlerContext ctx, GameMessage.GameOver gameOver) {
        logger.info("游戏结束: 是否胜利={}, 最终得分={}", gameOver.getIsWin(), gameOver.getFinalScore());
        // TODO: 实现游戏结束逻辑
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(ChannelHandlerContext ctx, int code, String description) {
        GameMessage.ErrorMessage error = GameMessage.ErrorMessage.newBuilder()
                .setCode(code)
                .setDescription(description)
                .build();

        sendMessage(ctx, GameMessage.MessageType.ERROR_MESSAGE, error);
    }

    /**
     * 发送消息给客户端
     */
    private void sendMessage(ChannelHandlerContext ctx, GameMessage.MessageType type, Object data) {
        GameMessage.Builder messageBuilder = GameMessage.newBuilder()
                .setType(type)
                .setTimestamp(System.currentTimeMillis())
                .setRequestId(java.util.UUID.randomUUID().toString());

        // 根据数据类型设置对应的字段
        if (data instanceof GameMessage.LoginResponse) {
            messageBuilder.setLoginResponse((GameMessage.LoginResponse) data);
        } else if (data instanceof GameMessage.MatchResult) {
            messageBuilder.setMatchResult((GameMessage.MatchResult) data);
        } else if (data instanceof GameMessage.ScoreUpdate) {
            messageBuilder.setScoreUpdate((GameMessage.ScoreUpdate) data);
        } else if (data instanceof GameMessage.ErrorMessage) {
            messageBuilder.setError((GameMessage.ErrorMessage) data);
        } else if (data instanceof GameMessage.GameOver) {
            messageBuilder.setGameOver((GameMessage.GameOver) data);
        }

        // 发送消息
        ctx.writeAndFlush(messageBuilder.build());
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        if (cause instanceof InvalidProtocolBufferException) {
            logger.error("Protobuf解码错误: {}", cause.getMessage());
            sendErrorMessage(ctx, 400, "消息格式错误");
        } else {
            logger.error("游戏消息处理异常", cause);
        }
        ctx.close();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        // 玩家断开连接时从在线列表移除
        onlinePlayers.values().remove(ctx);
        logger.info("玩家断开连接");
        super.channelInactive(ctx);
    }
}