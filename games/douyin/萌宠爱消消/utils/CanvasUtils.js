/**
 * Canvas工具类 - 提供Canvas兼容性支持和通用方法
 */

class CanvasUtils {
    /**
     * 为Canvas上下文添加roundRect兼容性支持
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     */
    static addRoundRectSupport(ctx) {
        if (!ctx.roundRect) {
            ctx.roundRect = function(x, y, width, height, radius) {
                if (typeof radius === 'undefined') {
                    radius = 5;
                }
                if (typeof radius === 'number') {
                    radius = {tl: radius, tr: radius, br: radius, bl: radius};
                } else {
                    var defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};
                    for (var side in defaultRadius) {
                        radius[side] = radius[side] || defaultRadius[side];
                    }
                }
                
                this.beginPath();
                this.moveTo(x + radius.tl, y);
                this.lineTo(x + width - radius.tr, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
                this.lineTo(x + width, y + height - radius.br);
                this.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
                this.lineTo(x + radius.bl, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
                this.lineTo(x, y + radius.tl);
                this.quadraticCurveTo(x, y, x + radius.tl, y);
                this.closePath();
            };
        }
    }
    
    /**
     * 绘制圆角矩形（兼容版本）
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {number} radius - 圆角半径
     */
    static drawRoundRect(ctx, x, y, width, height, radius) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }
        if (typeof radius === 'number') {
            radius = {tl: radius, tr: radius, br: radius, bl: radius};
        } else {
            var defaultRadius = {tl: 0, tr: 0, br: 0, bl: 0};
            for (var side in defaultRadius) {
                radius[side] = radius[side] || defaultRadius[side];
            }
        }
        
        ctx.beginPath();
        ctx.moveTo(x + radius.tl, y);
        ctx.lineTo(x + width - radius.tr, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
        ctx.lineTo(x + width, y + height - radius.br);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
        ctx.lineTo(x + radius.bl, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
        ctx.lineTo(x, y + radius.tl);
        ctx.quadraticCurveTo(x, y, x + radius.tl, y);
        ctx.closePath();
    }
    
    /**
     * 填充圆角矩形
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {number} radius - 圆角半径
     * @param {string} fillStyle - 填充样式
     */
    static fillRoundRect(ctx, x, y, width, height, radius, fillStyle) {
        ctx.save();
        ctx.fillStyle = fillStyle;
        this.drawRoundRect(ctx, x, y, width, height, radius);
        ctx.fill();
        ctx.restore();
    }
    
    /**
     * 描边圆角矩形
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} width - 宽度
     * @param {number} height - 高度
     * @param {number} radius - 圆角半径
     * @param {string} strokeStyle - 描边样式
     * @param {number} lineWidth - 线宽
     */
    static strokeRoundRect(ctx, x, y, width, height, radius, strokeStyle, lineWidth = 1) {
        ctx.save();
        ctx.strokeStyle = strokeStyle;
        ctx.lineWidth = lineWidth;
        this.drawRoundRect(ctx, x, y, width, height, radius);
        ctx.stroke();
        ctx.restore();
    }
    
    /**
     * 优化Canvas渲染设置
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     */
    static optimizeCanvasRendering(ctx) {
        // 添加roundRect支持
        this.addRoundRectSupport(ctx);
        
        // 优化文字渲染
        if (ctx.textRenderingOptimization) {
            ctx.textRenderingOptimization = 'optimizeQuality';
        }
        
        // 默认禁用图像平滑（适合像素艺术）
        ctx.imageSmoothingEnabled = false;
    }
    
    /**
     * 检查是否支持原生roundRect
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @returns {boolean} 是否支持
     */
    static supportsNativeRoundRect(ctx) {
        return typeof ctx.roundRect === 'function';
    }
}

module.exports = CanvasUtils;
