/**
 * 萌宠爱消消 - 游戏页面
 * 游戏主界面和逻辑实现
 */

const CONFIG = require('../config.js');
const BackgroundRenderer = require('../utils/BackgroundRenderer.js');
const ImageLoader = require('../utils/ImageLoader.js');
const UIComponents = require('../utils/UIComponents.js');
const CanvasUtils = require('../utils/CanvasUtils.js');

class GamePage {
    constructor(app) {
        this.app = app;
        this.canvas = app.canvas;
        this.ctx = app.ctx;
        
        // 游戏状态
        this.gameState = {
            board: [],
            selectedTile: null,
            isAnimating: false,
            score: 0,
            combo: 0,
            level: app.globalData.currentLevel || 1,
            targetScore: CONFIG.levels[(app.globalData.currentLevel || 1) - 1].targetScore,
            animalTypes: CONFIG.levels[(app.globalData.currentLevel || 1) - 1].animalTypes,
            props: {
                refresh: CONFIG.props.refresh.maxCount,
                bomb: CONFIG.props.bomb.maxCount,
                clear: CONFIG.props.clear.maxCount
            },
            reviveCount: 0
        };

        // 动画和特效
        this.particles = [];
        this.scorePopups = [];

        // 图片加载器
        this.imageLoader = new ImageLoader();

        // 拖拽状态
        this.dragState = {
            isDragging: false,
            startTile: null,
            currentPos: { x: 0, y: 0 },
            startPos: { x: 0, y: 0 },
            dragTile: null,
            dragOffset: { x: 0, y: 0 }
        };

        // 动画系统
        this.animations = {
            falling: [], // 下落动画
            explosions: [], // 爆炸效果
            scorePopups: [], // 分数弹出
            comboPopups: [] // 连击弹出
        };

        // 时间计时器
        this.gameTimer = {
            startTime: Date.now(),
            currentTime: 0,
            isRunning: true
        };

        // 启动计时器更新
        this.startTimer();

        // 动画帧控制
        this.animationFrameId = null;
        this.isDrawing = false;
        this.lastDrawTime = 0;
        this.drawInterval = 1000 / 30; // 限制为30FPS

        // 连击状态
        this.comboState = {
            count: 0,
            multiplier: 1,
            lastMatchTime: 0
        };

        // 布局计算
        this.layout = this.calculateLayout();

        // 加载图片资源
        this.loadImages();

        // 优化Canvas渲染设置
        CanvasUtils.optimizeCanvasRendering(this.ctx);

        this.init();
    }



    /**
     * 计算游戏布局
     */
    calculateLayout() {
        const displayWidth = this.app.displayWidth;
        const displayHeight = this.app.displayHeight;
        const spacing = 15; // spacing_var

        // 统计栏 - 减少高度
        const statsHeight = 100; // 从120减少到100
        const statsY = 120; // 进一步向下移动，避免刘海屏遮挡

        // 游戏区域（85%屏幕宽度，增大游戏区域）
        const gameAreaWidth = displayWidth * 0.85;
        const gameAreaX = (displayWidth - gameAreaWidth) / 2;

        // 网格计算：使用配置文件中的行列数，减小间距
        const gridPadding = 6;
        const gridSpacing = 2; // 从4减少到2，间距更紧凑
        const cols = CONFIG.board.cols;
        const rows = CONFIG.board.rows;
        const tileSize = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
        const gridHeight = tileSize * rows + gridSpacing * (rows - 1) + gridPadding * 2;
        const gridY = statsY + statsHeight + spacing;

        // 道具栏 - 减少高度
        const propBarHeight = 80; // 从100减少到80
        const propBarY = gridY + gridHeight + spacing;

        return {
            displayWidth,
            displayHeight,
            spacing,
            stats: { x: gameAreaX, y: statsY, width: gameAreaWidth, height: statsHeight },
            grid: { x: gameAreaX, y: gridY, width: gameAreaWidth, height: gridHeight, padding: gridPadding },
            tile: { size: tileSize, spacing: gridSpacing },
            propBar: { x: gameAreaX, y: propBarY, width: gameAreaWidth, height: propBarHeight }
        };
    }

    /**
     * 加载图片资源
     */
    async loadImages() {
        try {
            await Promise.all([
                this.imageLoader.loadAnimalImages(),
                this.imageLoader.loadPropImages()
            ]);
            console.log('所有图片加载完成');
        } catch (error) {
            console.error('图片加载失败:', error);
        }
    }

    /**
     * 初始化游戏页面
     */
    async init() {
        this.initBoard();

        // 先绘制一次（显示加载状态）
        this.draw();

        // 异步加载图片
        await this.loadImages();

        // 图片加载完成后重新绘制
        this.draw();

        this.bindEvents();
    }

    /**
     * 初始化游戏棋盘
     */
    initBoard() {
        const levelConfig = CONFIG.levels[this.gameState.level - 1];
        const animalCount = levelConfig.animalTypes;
        
        // 创建空棋盘
        this.gameState.board = [];
        for (let row = 0; row < CONFIG.board.rows; row++) {
            this.gameState.board[row] = [];
            for (let col = 0; col < CONFIG.board.cols; col++) {
                // 随机生成萌宠类型，但避免初始就有3消
                let animalType;
                do {
                    animalType = Math.floor(Math.random() * animalCount);
                } while (this.wouldCreateMatch(row, col, animalType));
                
                this.gameState.board[row][col] = {
                    type: animalType,
                    x: CONFIG.board.startX + col * (CONFIG.board.tileSize + CONFIG.board.padding),
                    y: CONFIG.board.startY + row * (CONFIG.board.tileSize + CONFIG.board.padding),
                    row: row,
                    col: col,
                    selected: false,
                    effect: null // 'rocket' 或 'bomb'
                };
            }
        }

        // 检查是否有可行移动
        if (!this.hasValidMoves()) {
            this.initBoard(); // 重新生成
        }
    }

    /**
     * 检查是否会创建消除
     */
    wouldCreateMatch(row, col, type) {
        // 检查横向
        let hCount = 1;
        for (let i = col - 1; i >= 0 && this.gameState.board[row] && this.gameState.board[row][i] && this.gameState.board[row][i].type === type; i--) {
            hCount++;
        }
        for (let i = col + 1; i < CONFIG.board.cols && this.gameState.board[row] && this.gameState.board[row][i] && this.gameState.board[row][i].type === type; i++) {
            hCount++;
        }
        if (hCount >= 3) return true;

        // 检查纵向
        let vCount = 1;
        for (let i = row - 1; i >= 0 && this.gameState.board[i] && this.gameState.board[i][col] && this.gameState.board[i][col].type === type; i--) {
            vCount++;
        }
        for (let i = row + 1; i < CONFIG.board.rows && this.gameState.board[i] && this.gameState.board[i][col] && this.gameState.board[i][col].type === type; i++) {
            vCount++;
        }
        if (vCount >= 3) return true;

        return false;
    }

    /**
     * 交换指定位置的方块
     */
    swapTilesByPosition(row1, col1, row2, col2) {
        const tile1 = this.gameState.board[row1][col1];
        const tile2 = this.gameState.board[row2][col2];
        
        if (tile1 && tile2) {
            const tempType = tile1.type;
            tile1.type = tile2.type;
            tile2.type = tempType;
            
            const tempEffect = tile1.effect;
            tile1.effect = tile2.effect;
            tile2.effect = tempEffect;
        }
    }

    /**
     * 检查是否有可行移动
     */
    hasValidMoves() {
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                // 检查与右边交换
                if (col < CONFIG.board.cols - 1) {
                    this.swapTilesByPosition(row, col, row, col + 1);
                    if (this.findMatches().length > 0) {
                        this.swapTilesByPosition(row, col, row, col + 1); // 交换回来
                        return true;
                    }
                    this.swapTilesByPosition(row, col, row, col + 1); // 交换回来
                }

                // 检查与下边交换
                if (row < CONFIG.board.rows - 1) {
                    this.swapTilesByPosition(row, col, row + 1, col);
                    if (this.findMatches().length > 0) {
                        this.swapTilesByPosition(row, col, row + 1, col); // 交换回来
                        return true;
                    }
                    this.swapTilesByPosition(row, col, row + 1, col); // 交换回来
                }
            }
        }
        return false;
    }

    /**
     * 绘制游戏界面
     */
    draw() {
        // 帧率控制：限制绘制频率
        const now = Date.now();
        if (now - this.lastDrawTime < this.drawInterval) {
            return; // 跳过这次绘制
        }

        // 防止重复绘制
        if (this.isDrawing) {
            return;
        }

        this.isDrawing = true;
        this.lastDrawTime = now;

        try {
            this.clearCanvas();
            this.drawBackground();
            this.drawBackButton();
            this.drawStatsBar();
            this.drawGameGrid();
            this.drawPropBar();
            this.drawParticles();
            this.drawScorePopups();

            // 更新和绘制新动画系统
            this.updateAnimations();
            this.drawAnimations();

            // 如果显示确认对话框
            if (this.showingExitDialog) {
                this.drawExitDialog();
            }
        } catch (error) {
            console.error('绘制错误:', error);
            // 清理动画状态，防止卡住
            this.emergencyReset();
        } finally {
            this.isDrawing = false;
        }
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.ctx.clearRect(0, 0, this.app.displayWidth, this.app.displayHeight);
    }

    /**
     * 绘制背景
     */
    drawBackground() {
        BackgroundRenderer.drawGradientBackground(this.ctx, this.app.displayWidth, this.app.displayHeight);
    }

    /**
     * 绘制返回按钮
     */
    drawBackButton() {
        const buttonSize = 40;
        const buttonX = 20;
        const buttonY = 60; // 避免刘海屏遮挡

        // 存储返回按钮位置
        this.backButton = {
            x: buttonX,
            y: buttonY,
            width: buttonSize,
            height: buttonSize
        };

        UIComponents.drawBackButton(this.ctx, buttonX, buttonY, buttonSize);
    }

    /**
     * 绘制退出确认对话框
     */
    drawExitDialog() {
        const ctx = this.ctx;
        const dialogWidth = 280;
        const dialogHeight = 200; // 增加高度20
        const dialogX = (this.app.displayWidth - dialogWidth) / 2;
        const dialogY = (this.app.displayHeight - dialogHeight) / 2;

        // 半透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, 0, this.app.displayWidth, this.app.displayHeight);

        // 对话框背景
        ctx.save();
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 4;
        ctx.shadowBlur = 12;

        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.beginPath();
        ctx.roundRect(dialogX, dialogY, dialogWidth, dialogHeight, 16);
        ctx.fill();

        ctx.restore();

        // 标题
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('确认退出', dialogX + dialogWidth/2, dialogY + 50);

        // 提示文字
        ctx.fillStyle = CONFIG.ui.colors.textLight;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText('游戏进度将会丢失', dialogX + dialogWidth/2, dialogY + 80);
        ctx.fillText('确定要退出吗？', dialogX + dialogWidth/2, dialogY + 105);

        // 按钮
        const buttonWidth = 100;
        const buttonHeight = 40;
        const button1X = dialogX + 40;
        const button2X = dialogX + dialogWidth - buttonWidth - 40;
        const buttonY = dialogY + 130;

        // 继续按钮
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.beginPath();
        ctx.roundRect(button1X, buttonY, buttonWidth, buttonHeight, 8);
        ctx.fill();

        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText('继续', button1X + buttonWidth/2, buttonY + buttonHeight/2 + 6);

        // 退出按钮
        ctx.fillStyle = '#9E9E9E';
        ctx.beginPath();
        ctx.roundRect(button2X, buttonY, buttonWidth, buttonHeight, 8);
        ctx.fill();

        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.fillText('退出', button2X + buttonWidth/2, buttonY + buttonHeight/2 + 6);

        // 存储按钮位置
        this.exitDialogButtons = {
            continue: { x: button1X, y: buttonY, width: buttonWidth, height: buttonHeight },
            exit: { x: button2X, y: buttonY, width: buttonWidth, height: buttonHeight }
        };
    }

    /**
     * 绘制统计栏（按需求文档设计）
     */
    drawStatsBar() {
        const ctx = this.ctx;
        const layout = this.layout.stats;
        const levelConfig = CONFIG.levels[this.gameState.level - 1];

        // 统计栏背景
        BackgroundRenderer.drawTransparentCard(ctx, layout.x, layout.y, layout.width, layout.height, 15, 0.85);

        // 关卡标题
        ctx.fillStyle = CONFIG.ui.colors.primary;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText(levelConfig.title, layout.x + layout.width/2, layout.y + 25);

        // 分数、时间和目标分数 - 向上调整位置
        const scoreY = layout.y + 50; // 从75调整到50
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;

        // 左侧：分数
        ctx.textAlign = 'left';
        ctx.fillText(`分数: ${this.gameState.score}`, layout.x + 20, scoreY);

        // 中间：时间计时器
        ctx.textAlign = 'center';
        const currentTime = this.gameTimer.currentTime;
        const minutes = Math.floor(currentTime / 60);
        const seconds = currentTime % 60;
        const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        ctx.fillStyle = '#FF6B35'; // 橙色时间显示
        ctx.fillText(`⏱️ ${timeStr}`, layout.x + layout.width/2, scoreY);

        // 右侧：目标分数
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.textAlign = 'right';
        ctx.fillText(`目标: ${this.gameState.targetScore}`, layout.x + layout.width - 20, scoreY);

        // 进度条 - 向上调整位置
        const progressY = layout.y + 70; // 从90调整到70
        const progressWidth = layout.width - 40;
        const progressHeight = 8;
        const progress = Math.min(1, this.gameState.score / this.gameState.targetScore);

        UIComponents.drawProgressBar(ctx, layout.x + 20, progressY, progressWidth, progressHeight, progress, CONFIG.ui.colors.primary);

        // 连击显示 - 向上调整位置
        if (this.comboState.count > 1) {
            ctx.fillStyle = '#FF6B35';
            ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText(`连击 x${this.comboState.count} (${this.comboState.multiplier}x倍率)`, layout.x + layout.width/2, layout.y + 95); // 从115调整到95
        }
    }

    /**
     * 绘制UI信息
     */
    drawUI() {
        const ctx = this.ctx;
        const displayWidth = this.app.displayWidth;
        const displayHeight = this.app.displayHeight;

        // 动态计算字体大小
        const scoreFontSize = Math.min(24, displayWidth * 0.06);
        const titleFontSize = Math.min(20, displayWidth * 0.05);
        const comboFontSize = Math.min(28, displayWidth * 0.07);

        // 绘制关卡信息
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${titleFontSize}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText(`第 ${this.gameState.level} 关`, displayWidth / 2, 40);

        // 绘制分数
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${scoreFontSize}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'left';
        ctx.fillText(`分数: ${this.gameState.score}`, 20, 80);

        // 绘制目标分数
        ctx.textAlign = 'right';
        ctx.fillText(`目标: ${this.gameState.targetScore}`, displayWidth - 20, 80);

        // 绘制连击信息
        if (this.gameState.combo > 1) {
            ctx.fillStyle = CONFIG.ui.colors.primary;
            ctx.font = `bold ${comboFontSize}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText(`连击 x${this.gameState.combo}`, displayWidth / 2, 120);
        }
    }

    /**
     * 绘制游戏网格（美化版本）
     */
    drawGameGrid() {
        const ctx = this.ctx;
        const layout = this.layout.grid;
        const tileSize = this.layout.tile.size;
        const spacing = this.layout.tile.spacing;

        // 网格背景（立体感）
        ctx.save();

        // 外层阴影
        ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 4;
        ctx.shadowBlur = 8;

        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.roundRect(layout.x, layout.y, layout.width, layout.height, 20);
        ctx.fill();

        ctx.restore();

        // 内层边框
        ctx.strokeStyle = '#D0D0D0';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(layout.x + 2, layout.y + 2, layout.width - 4, layout.height - 4, 18);
        ctx.stroke();

        // 绘制网格槽位（凹陷效果） - 使用与calculateLayout相同的坐标计算
        const displayWidth = this.app.displayWidth;
        const spacing_var = 15; // spacing_var
        const statsHeight = 100;
        const statsY = 120;
        const gameAreaWidth = displayWidth * 0.85;
        const gameAreaX = (displayWidth - gameAreaWidth) / 2;
        const gridPadding = 6;
        const gridSpacing = 2;
        const cols = CONFIG.board.cols;
        const tileSize_calc = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
        const gridY = statsY + statsHeight + spacing_var;
        const gridX = gameAreaX;

        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                const x = gridX + gridPadding + col * (tileSize_calc + gridSpacing);
                const y = gridY + gridPadding + row * (tileSize_calc + gridSpacing);

                // 槽位阴影（凹陷效果）
                ctx.fillStyle = 'rgba(0, 0, 0, 0.08)';
                ctx.beginPath();
                ctx.roundRect(x + 1, y + 1, tileSize_calc - 2, tileSize_calc - 2, 6);
                ctx.fill();

                // 槽位背景 - 更浅的背景色
                ctx.fillStyle = '#FAFAFA';
                ctx.beginPath();
                ctx.roundRect(x, y, tileSize_calc, tileSize_calc, 6);
                ctx.fill();

                // 槽位边框
                ctx.strokeStyle = '#E8E8E8';
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        }

        // 绘制萌宠
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                const tile = this.gameState.board[row] && this.gameState.board[row][col];
                if (tile && tile.type >= 0) {
                    this.drawAnimalTile(tile, row, col);
                }
            }
        }
    }

    /**
     * 绘制萌宠方块（美化版本）
     */
    drawAnimalTile(tile, row, col) {
        const ctx = this.ctx;

        // 使用与calculateLayout完全相同的布局计算
        const displayWidth = this.app.displayWidth;
        const spacing_var = 15; // spacing_var
        const statsHeight = 100;
        const statsY = 120;
        const gameAreaWidth = displayWidth * 0.85;
        const gameAreaX = (displayWidth - gameAreaWidth) / 2;
        const gridPadding = 6;
        const gridSpacing = 2;
        const cols = CONFIG.board.cols;
        const tileSize = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
        const gridY = statsY + statsHeight + spacing_var;
        const gridX = gameAreaX;

        let x = gridX + gridPadding + col * (tileSize + gridSpacing);
        let y = gridY + gridPadding + row * (tileSize + gridSpacing);

        // 检查是否是正在拖拽的方块
        const isDragging = this.dragState.isDragging &&
                          this.dragState.startTile &&
                          this.dragState.startTile.row === row &&
                          this.dragState.startTile.col === col;

        if (isDragging) {
            // 添加拖拽偏移
            x += this.dragState.dragOffset.x;
            y += this.dragState.dragOffset.y;
        }

        const animal = CONFIG.animals.types[tile.type];
        if (!animal) return;

        // 方块背景 - 每个萌宠有独特的浅背景色
        ctx.save();

        // 阴影效果 - 拖拽时增强阴影
        if (isDragging) {
            ctx.shadowColor = 'rgba(0, 0, 0, 0.4)';
            ctx.shadowOffsetX = 3;
            ctx.shadowOffsetY = 5;
            ctx.shadowBlur = 8;
        } else {
            ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 1;
            ctx.shadowBlur = 3;
        }

        // 根据动物类型设置不同的浅背景色
        const animalBgColors = [
            '#FFF3E0', // 猫咪 - 浅橙色背景
            '#E3F2FD', // 小狗 - 浅蓝色背景
            '#F3E5F5', // 大象 - 浅紫色背景
            '#FBE9E7', // 狐狸 - 浅红色背景
            '#E8F5E8', // 青蛙 - 浅绿色背景
            '#EFEBE9', // 猴子 - 浅棕色背景
            '#F5F5F5', // 熊猫 - 浅灰色背景
            '#FCE4EC', // 兔子 - 浅粉色背景
            '#FFF8E1'  // 老虎 - 浅黄色背景
        ];

        ctx.fillStyle = animalBgColors[tile.type] || '#FFFFFF';
        ctx.beginPath();
        ctx.roundRect(x + 1, y + 1, tileSize - 2, tileSize - 2, 6);
        ctx.fill();

        ctx.restore();

        // 方块边框（移除选中框，只在拖拽时显示）
        ctx.strokeStyle = (tile.selected && this.dragState.isDragging) ? CONFIG.ui.colors.primary : '#D0D0D0';
        ctx.lineWidth = (tile.selected && this.dragState.isDragging) ? 3 : 1;
        ctx.beginPath();
        ctx.roundRect(x + 2, y + 2, tileSize - 4, tileSize - 4, 8);
        ctx.stroke();

        // 绘制萌宠图片（禁用emoji）- 增大图片尺寸，使用整数坐标
        const imgSize = Math.round(tileSize * 0.8); // 从0.7增加到0.8，使用整数
        const imgX = Math.round(x + (tileSize - imgSize) / 2);
        const imgY = Math.round(y + (tileSize - imgSize) / 2);

        this.imageLoader.drawCrispImage(
            ctx,
            animal.id.toString(), // 确保使用字符串key
            imgX,
            imgY,
            imgSize,
            imgSize,
            animal.name // 使用动物名称作为备用文字
        );

        // 特效方块标识
        if (tile.special) {
            this.drawSpecialEffect(x, y, tileSize, tile.special);
        }
    }

    /**
     * 绘制特效标识
     */
    drawSpecialEffect(x, y, size, effectType) {
        const ctx = this.ctx;

        ctx.save();
        ctx.globalAlpha = 0.9;

        if (effectType === 'rocket') {
            // 小火箭标识
            ctx.fillStyle = '#FF9800';
            ctx.beginPath();
            ctx.arc(x + size - 12, y + 12, 8, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#FFFFFF';
            ctx.font = `${size * 0.2}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText('🚀', x + size - 12, y + 16);

        } else if (effectType === 'bomb') {
            // 小炸弹标识
            ctx.fillStyle = '#F44336';
            ctx.beginPath();
            ctx.arc(x + size - 12, y + 12, 8, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#FFFFFF';
            ctx.font = `${size * 0.2}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText('💣', x + size - 12, y + 16);
        }

        ctx.restore();
    }

    /**
     * 绘制粒子效果
     */
    drawParticles() {
        const ctx = this.ctx;

        this.particles.forEach((particle, index) => {
            ctx.save();
            ctx.globalAlpha = particle.alpha;
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, 2 * Math.PI);
            ctx.fill();
            ctx.restore();

            // 更新粒子
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.vy += 0.2; // 重力
            particle.alpha -= 0.02;
            particle.size *= 0.98;

            // 移除消失的粒子
            if (particle.alpha <= 0 || particle.size <= 0.1) {
                this.particles.splice(index, 1);
            }
        });
    }

    /**
     * 绘制分数弹出效果
     */
    drawScorePopups() {
        const ctx = this.ctx;

        this.scorePopups.forEach((popup, index) => {
            ctx.save();
            ctx.globalAlpha = popup.alpha;
            ctx.fillStyle = popup.color;
            ctx.font = `bold ${popup.size}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 2;
            ctx.strokeText(popup.text, popup.x, popup.y);
            ctx.fillText(popup.text, popup.x, popup.y);
            ctx.restore();

            // 更新弹出效果
            popup.y -= popup.vy;
            popup.alpha -= 0.02;
            popup.size *= 1.01;

            // 移除消失的弹出效果
            if (popup.alpha <= 0) {
                this.scorePopups.splice(index, 1);
            }
        });
    }

    /**
     * 创建粒子效果
     */
    createParticles(x, y, color, count = 10) {
        for (let i = 0; i < count; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8 - 2,
                size: Math.random() * 4 + 2,
                alpha: 1,
                color: color
            });
        }
    }

    /**
     * 创建分数弹出效果
     */
    createScorePopup(x, y, score, isCombo = false) {
        this.scorePopups.push({
            x: x,
            y: y,
            text: isCombo ? `连击 x${score}` : `+${score}`,
            size: isCombo ? 24 : 20,
            alpha: 1,
            vy: 2,
            color: isCombo ? '#FF6B35' : CONFIG.ui.colors.primary
        });
    }

    /**
     * 绘制道具栏（按新布局设计）
     */
    drawPropBar() {
        const ctx = this.ctx;
        const layout = this.layout.propBar;
        const props = ['refresh', 'bomb', 'clear'];

        // 道具栏背景（立体感）
        ctx.save();

        // 外层阴影
        ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = -2;
        ctx.shadowBlur = 6;

        ctx.fillStyle = 'rgba(255, 255, 255, 0.85)';
        ctx.beginPath();
        ctx.roundRect(layout.x, layout.y, layout.width, layout.height, 15);
        ctx.fill();

        ctx.restore();

        // 内层边框
        ctx.strokeStyle = '#D0D0D0';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(layout.x + 2, layout.y + 2, layout.width - 4, layout.height - 4, 13);
        ctx.stroke();

        // 存储道具位置信息供点击检测使用
        this.propButtons = [];

        // 计算道具按钮布局 - 减少道具卡大小
        const propSize = 55; // 从60减少到55
        const propSpacing = (layout.width - props.length * propSize) / (props.length + 1);

        props.forEach((prop, index) => {
            const x = layout.x + propSpacing + index * (propSize + propSpacing);
            const y = layout.y + (layout.height - propSize) / 2;
            const count = this.gameState.props[prop];
            const isAvailable = count > 0 && prop !== 'clear'; // 清屏卡始终不可用

            // 存储道具按钮信息
            this.propButtons.push({
                type: prop,
                x: x,
                y: y,
                width: propSize,
                height: propSize,
                available: isAvailable
            });

            // 道具按钮背景（立体感）
            ctx.save();

            // 按钮阴影（灰色状态时减弱）
            ctx.shadowColor = isAvailable ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)';
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = isAvailable ? 2 : 1;
            ctx.shadowBlur = isAvailable ? 4 : 2;

            // 按钮渐变背景
            const btnGradient = ctx.createLinearGradient(x, y, x, y + propSize);
            if (isAvailable) {
                btnGradient.addColorStop(0, CONFIG.ui.colors.primary);
                btnGradient.addColorStop(1, '#E91E63');
            } else {
                // 灰色不可用状态
                btnGradient.addColorStop(0, '#CCCCCC');
                btnGradient.addColorStop(1, '#999999');
            }

            ctx.fillStyle = btnGradient;
            ctx.beginPath();
            ctx.roundRect(x, y, propSize, propSize, 12);
            ctx.fill();

            ctx.restore();

            // 道具图标 - 使用整数坐标提高清晰度
            const propConfig = CONFIG.props[prop];
            const imgSize = Math.round(propSize * 0.6);
            const imgX = Math.round(x + (propSize - imgSize) / 2);
            const imgY = Math.round(y + (propSize - imgSize) / 2 - 5);

            this.imageLoader.drawCrispImage(
                ctx,
                prop,
                imgX,
                imgY,
                imgSize,
                imgSize,
                propConfig.name, // 使用道具名称作为备用文字
                !isAvailable // 不可用时显示灰度
            );

            // 道具名称 - 增大字体，提高清晰度
            ctx.fillStyle = isAvailable ? CONFIG.ui.colors.white : '#666666';
            ctx.font = `bold ${propSize * 0.18}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 添加文字阴影提高可读性
            ctx.save();
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowOffsetX = 1;
            ctx.shadowOffsetY = 1;
            ctx.shadowBlur = 2;
            ctx.fillText(propConfig.name, x + propSize/2, y + propSize - 10);
            ctx.restore();

            // 数量显示 - 只有数量大于0且不是清屏卡时才显示徽章
            if (count > 0 && prop !== 'clear') {
                // 数量徽章背景
                ctx.fillStyle = '#FF3B30';
                ctx.beginPath();
                ctx.arc(x + propSize - 10, y + 10, 12, 0, 2 * Math.PI);
                ctx.fill();

                // 徽章边框
                ctx.strokeStyle = CONFIG.ui.colors.white;
                ctx.lineWidth = 2;
                ctx.stroke();

                // 数量文字
                ctx.fillStyle = CONFIG.ui.colors.white;
                ctx.font = `bold 12px ${CONFIG.ui.fonts.primary}`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(count.toString(), x + propSize - 10, y + 10);
            }
        });

        // 暂停按钮已删除，避免与清屏卡位置冲突
    }

    /**
     * 使用道具
     */
    useProp(propType) {
        if (this.gameState.props[propType] <= 0 || this.gameState.isAnimating) {
            return false;
        }

        this.gameState.props[propType]--;

        switch (propType) {
            case 'refresh':
                this.shuffleBoard();
                this.app.playSound('match');
                break;
            case 'bomb':
                this.useBombProp();
                break;
            case 'clear':
                this.useClearProp();
                break;
        }

        this.draw();
        return true;
    }

    /**
     * 使用炸弹道具
     */
    useBombProp() {
        // 在棋盘中心创建5x5爆炸
        const centerRow = Math.floor(CONFIG.board.rows / 2);
        const centerCol = Math.floor(CONFIG.board.cols / 2);

        for (let row = centerRow - 2; row <= centerRow + 2; row++) {
            for (let col = centerCol - 2; col <= centerCol + 2; col++) {
                if (row >= 0 && row < CONFIG.board.rows &&
                    col >= 0 && col < CONFIG.board.cols) {
                    this.gameState.board[row][col].type = -1;
                }
            }
        }

        this.gameState.score += CONFIG.props.bomb.score;
        this.app.playSound('bomb');

        // 触发下落和填充
        setTimeout(() => {
            this.dropTiles();
            this.fillEmpty();
            this.draw();
        }, 500);
    }

    /**
     * 使用清屏道具
     */
    useClearProp() {
        // 清除所有动物
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                this.gameState.board[row][col].type = -1;
            }
        }

        this.gameState.score += CONFIG.props.clear.score;
        this.app.playSound('bomb');

        // 触发填充
        setTimeout(() => {
            this.fillEmpty();
            this.draw();
        }, 500);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        tt.onTouchStart(this.handleTouchStart.bind(this));
        tt.onTouchMove(this.handleTouchMove.bind(this));
        tt.onTouchEnd(this.handleTouchEnd.bind(this));
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(e) {
        // 检查道具点击，道具点击不受动画状态限制
        const touch = e.touches[0];
        const x = touch.clientX;
        const y = touch.clientY;

        // 优先检查道具点击
        if (this.checkPropClick(x, y)) {
            return;
        }

        // 其他操作需要等待动画完成
        if (this.gameState.isAnimating) return;

        // x和y已经在上面定义了

        // 如果显示退出对话框，处理对话框点击
        if (this.showingExitDialog) {
            this.handleExitDialogClick(x, y);
            return;
        }

        // 检查返回按钮
        if (this.backButton && this.isPointInRect(x, y, this.backButton.x, this.backButton.y, this.backButton.width, this.backButton.height)) {
            this.showingExitDialog = true;
            this.draw();
            return;
        }

        // 检查道具点击
        if (this.checkPropClick(x, y)) {
            return;
        }

        // 暂停按钮已删除

        // 开始拖拽
        const tilePos = this.getTilePositionAtPoint(x, y);
        if (tilePos) {
            const tile = this.gameState.board[tilePos.row] && this.gameState.board[tilePos.row][tilePos.col];
            if (tile && tile.type >= 0) {
                this.dragState.isDragging = true;
                this.dragState.startTile = { row: tilePos.row, col: tilePos.col };
                this.dragState.startPos = { x, y };
                this.dragState.currentPos = { x, y };
                this.dragState.dragTile = { ...tile }; // 复制拖拽的方块
                this.dragState.dragOffset = { x: 0, y: 0 };

                // 高亮选中的方块
                tile.selected = true;

                // 播放拖拽开始音效
                this.app.playSound('select');
                this.draw();
            }
        }
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(e) {
        if (!this.dragState.isDragging) return;

        const touch = e.touches[0];
        this.dragState.currentPos = { x: touch.clientX, y: touch.clientY };

        // 计算拖拽偏移
        this.dragState.dragOffset = {
            x: touch.clientX - this.dragState.startPos.x,
            y: touch.clientY - this.dragState.startPos.y
        };

        // 添加拖拽视觉反馈
        this.draw();
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(e) {
        if (!this.dragState.isDragging) return;

        const endPos = this.dragState.currentPos;
        const startTile = this.dragState.startTile;

        // 计算拖拽方向
        const deltaX = endPos.x - this.dragState.startPos.x;
        const deltaY = endPos.y - this.dragState.startPos.y;
        const minDragDistance = 30; // 最小拖拽距离

        if (Math.abs(deltaX) > minDragDistance || Math.abs(deltaY) > minDragDistance) {
            // 确定拖拽方向
            let targetRow = startTile.row;
            let targetCol = startTile.col;

            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // 水平拖拽
                targetCol += deltaX > 0 ? 1 : -1;
            } else {
                // 垂直拖拽
                targetRow += deltaY > 0 ? 1 : -1;
            }

            // 检查目标位置是否有效
            if (targetRow >= 0 && targetRow < CONFIG.board.rows && targetCol >= 0 && targetCol < CONFIG.board.cols) {
                const targetTile = this.gameState.board[targetRow] && this.gameState.board[targetRow][targetCol];
                if (targetTile && targetTile.type >= 0) {
                    // 执行交换（使用坐标版本的swapTiles）
                    this.swapTiles(startTile.row, startTile.col, targetRow, targetCol);
                }
            }
        }

        // 重置拖拽状态
        this.resetDragState();
        this.draw();
    }

    /**
     * 重置拖拽状态
     */
    resetDragState() {
        // 清除选中状态
        if (this.dragState.startTile) {
            const tile = this.gameState.board[this.dragState.startTile.row] &&
                        this.gameState.board[this.dragState.startTile.row][this.dragState.startTile.col];
            if (tile) {
                tile.selected = false;
            }
        }

        this.dragState = {
            isDragging: false,
            startTile: null,
            currentPos: { x: 0, y: 0 },
            startPos: { x: 0, y: 0 },
            dragTile: null,
            dragOffset: { x: 0, y: 0 }
        };
    }

    /**
     * 处理退出对话框点击
     */
    handleExitDialogClick(x, y) {
        if (!this.exitDialogButtons) return;

        const buttons = this.exitDialogButtons;

        // 继续游戏
        if (this.isPointInRect(x, y, buttons.continue.x, buttons.continue.y, buttons.continue.width, buttons.continue.height)) {
            this.showingExitDialog = false;
            this.draw();
        }
        // 退出游戏
        else if (this.isPointInRect(x, y, buttons.exit.x, buttons.exit.y, buttons.exit.width, buttons.exit.height)) {
            this.app.playSound('match');
            this.destroy();
            this.app.showHomePage();
        }
    }

    /**
     * 获取点击位置的方块坐标
     */
    getTilePositionAtPoint(x, y) {
        // 使用与calculateLayout完全相同的布局计算
        const displayWidth = this.app.displayWidth;
        const displayHeight = this.app.displayHeight;
        const spacing = 15; // spacing_var

        // 统计栏
        const statsHeight = 100;
        const statsY = 120; // 进一步向下移动，避免刘海屏遮挡

        // 游戏区域（85%屏幕宽度，增大游戏区域）
        const gameAreaWidth = displayWidth * 0.85;
        const gameAreaX = (displayWidth - gameAreaWidth) / 2;

        // 网格计算：使用配置文件中的行列数，减小间距
        const gridPadding = 6;
        const gridSpacing = 2;
        const cols = CONFIG.board.cols;
        const rows = CONFIG.board.rows;
        const tileSize = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
        const gridHeight = tileSize * rows + gridSpacing * (rows - 1) + gridPadding * 2;
        const gridY = statsY + statsHeight + spacing; // 与calculateLayout完全一致

        // 网格位置
        const gridX = gameAreaX;

        // 检查是否在网格区域内
        if (x < gridX + gridPadding || x > gridX + gameAreaWidth - gridPadding ||
            y < gridY + gridPadding || y > gridY + gridHeight - gridPadding) {
            return null;
        }

        // 计算方块坐标
        const relativeX = x - gridX - gridPadding;
        const relativeY = y - gridY - gridPadding;

        const col = Math.floor(relativeX / (tileSize + gridSpacing));
        const row = Math.floor(relativeY / (tileSize + gridSpacing));

        // 检查是否在有效范围内
        if (row >= 0 && row < rows && col >= 0 && col < cols) {
            // 检查是否点击在方块内部（不是间隙）
            const tileX = col * (tileSize + gridSpacing);
            const tileY = row * (tileSize + gridSpacing);

            if (relativeX >= tileX && relativeX <= tileX + tileSize &&
                relativeY >= tileY && relativeY <= tileY + tileSize) {
                return { row, col };
            }
        }

        return null;
    }

    /**
     * 交换两个方块
     */
    swapTiles(row1, col1, row2, col2) {
        const tile1 = this.gameState.board[row1][col1];
        const tile2 = this.gameState.board[row2][col2];

        // 交换方块类型
        const tempType = tile1.type;
        const tempSpecial = tile1.special;

        tile1.type = tile2.type;
        tile1.special = tile2.special;

        tile2.type = tempType;
        tile2.special = tempSpecial;

        // 检查是否形成消除
        const matches = this.findMatches();
        if (matches.length > 0) {
            // 有消除，执行消除逻辑
            this.processMatches(matches);
        } else {
            // 没有消除，交换回来
            setTimeout(() => {
                const currentType1 = tile1.type;
                const currentSpecial1 = tile1.special;

                tile1.type = tempType;
                tile1.special = tempSpecial;
                tile2.type = currentType1;
                tile2.special = currentSpecial1;
                this.draw();
            }, 300);
        }
    }

    /**
     * 查找匹配的方块
     */
    findMatches() {
        const matches = [];
        const board = this.gameState.board;
        const visited = Array(CONFIG.board.rows).fill().map(() => Array(CONFIG.board.cols).fill(false));

        // 检查水平匹配
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols - 2; col++) { // 最多到cols-3，因为要检查3个连续
                if (!board[row] || !board[row][col] || board[row][col].type < 0) continue;

                const type = board[row][col].type;
                let count = 1;
                let matchTiles = [{ row, col }];

                // 向右检查
                for (let c = col + 1; c < CONFIG.board.cols && board[row][c] && board[row][c].type === type; c++) {
                    count++;
                    matchTiles.push({ row, col: c });
                }

                if (count >= 3) {
                    matches.push({ tiles: matchTiles, count, type: 'horizontal' });
                    matchTiles.forEach(tile => visited[tile.row][tile.col] = true);
                }
            }
        }

        // 检查垂直匹配
        for (let col = 0; col < CONFIG.board.cols; col++) {
            for (let row = 0; row < CONFIG.board.rows - 2; row++) { // 最多到rows-3，因为要检查3个连续
                if (!board[row] || !board[row][col] || board[row][col].type < 0) continue;
                if (visited[row][col]) continue; // 已经被水平匹配包含

                const type = board[row][col].type;
                let count = 1;
                let matchTiles = [{ row, col }];

                // 向下检查
                for (let r = row + 1; r < CONFIG.board.rows && board[r] && board[r][col] && board[r][col].type === type; r++) {
                    count++;
                    matchTiles.push({ row: r, col });
                }

                if (count >= 3) {
                    matches.push({ tiles: matchTiles, count, type: 'vertical' });
                }
            }
        }

        return matches;
    }

    /**
     * 处理匹配消除
     */
    processMatches(matches = null) {
        // 如果没有传入matches，自动查找
        if (!matches) {
            matches = this.findMatches();
        }

        if (matches.length === 0) {
            // 没有匹配，重置连击和动画状态
            this.comboState.count = 0;
            this.comboState.multiplier = 1;
            this.gameState.isAnimating = false;

            // 确保清理所有动画
            this.animations.falling = [];
            this.animations.explosions = [];
            this.animations.scorePopups = [];
            this.animations.comboPopups = [];

            return;
        }

        this.gameState.isAnimating = true;
        let totalScore = 0;

        // 更新连击状态
        const now = Date.now();
        if (now - this.comboState.lastMatchTime < 2000) { // 2秒内算连击
            this.comboState.count++;
        } else {
            this.comboState.count = 1;
        }
        this.comboState.lastMatchTime = now;

        // 计算连击倍率
        if (this.comboState.count <= 3) {
            this.comboState.multiplier = 1;
        } else if (this.comboState.count <= 5) {
            this.comboState.multiplier = 1.5;
        } else {
            this.comboState.multiplier = 2;
        }

        matches.forEach(match => {
            const { tiles, count } = match;

            // 计算基础分数
            let baseScore = 0;
            if (count === 3) baseScore = 30;
            else if (count === 4) baseScore = 40;
            else if (count >= 5) baseScore = 50;

            // 应用连击倍率
            const finalScore = Math.floor(baseScore * this.comboState.multiplier);
            totalScore += finalScore;

            // 创建特效方块
            if (count === 4) {
                // 4消生成小火箭
                const centerTile = tiles[Math.floor(tiles.length / 2)];
                this.gameState.board[centerTile.row][centerTile.col].special = 'rocket';
            } else if (count >= 5) {
                // 5消生成小炸弹
                const centerTile = tiles[Math.floor(tiles.length / 2)];
                this.gameState.board[centerTile.row][centerTile.col].special = 'bomb';
            }

            // 移除匹配的方块（除了特效方块）
            tiles.forEach(tile => {
                // 验证tile对象和坐标有效性
                if (!tile || typeof tile.row !== 'number' || typeof tile.col !== 'number') {
                    console.warn('无效的方块位置:', tile);
                    return;
                }

                // 验证坐标范围
                if (tile.row < 0 || tile.row >= CONFIG.board.rows ||
                    tile.col < 0 || tile.col >= CONFIG.board.cols) {
                    console.warn('方块位置超出范围:', tile);
                    return;
                }

                // 验证棋盘位置存在
                if (!this.gameState.board[tile.row] || !this.gameState.board[tile.row][tile.col]) {
                    console.warn('棋盘位置不存在:', tile.row, tile.col);
                    return;
                }

                if (!this.gameState.board[tile.row][tile.col].special) {
                    this.gameState.board[tile.row][tile.col].type = -1;
                }

                // 创建粒子效果 - 使用与calculateLayout完全相同的布局计算
                const displayWidth = this.app.displayWidth;
                const spacing = 15; // spacing_var
                const statsHeight = 100;
                const statsY = 120;
                const gameAreaWidth = displayWidth * 0.85;
                const gameAreaX = (displayWidth - gameAreaWidth) / 2;
                const gridPadding = 6;
                const gridSpacing = 2;
                const cols = CONFIG.board.cols;
                const tileSize = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
                const gridY = statsY + statsHeight + spacing;
                const gridX = gameAreaX;

                // 只有在坐标有效时才创建特效
                if (typeof tile.row === 'number' && typeof tile.col === 'number') {
                    const x = gridX + gridPadding + tile.col * (tileSize + gridSpacing) + tileSize/2;
                    const y = gridY + gridPadding + tile.row * (tileSize + gridSpacing) + tileSize/2;

                    // 创建爆炸效果和粒子效果
                    this.createExplosion(x, y, '#FFD700');
                    this.createParticles(x, y, '#FFD700', 8);
                }
            });

            // 创建分数弹出效果 - 使用与calculateLayout完全相同的布局计算
            const centerTile = tiles[Math.floor(tiles.length / 2)];

            // 验证centerTile有效性
            if (centerTile && typeof centerTile.row === 'number' && typeof centerTile.col === 'number') {
                const displayWidth = this.app.displayWidth;
                const spacing = 15; // spacing_var
                const statsHeight = 100;
                const statsY = 120;
                const gameAreaWidth = displayWidth * 0.85;
                const gameAreaX = (displayWidth - gameAreaWidth) / 2;
                const gridPadding = 6;
                const gridSpacing = 2;
                const cols = CONFIG.board.cols;
                const tileSize = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
                const gridY = statsY + statsHeight + spacing;
                const gridX = gameAreaX;

                const x = gridX + gridPadding + centerTile.col * (tileSize + gridSpacing) + tileSize/2;
                const y = gridY + gridPadding + centerTile.row * (tileSize + gridSpacing) + tileSize/2;

                // 创建分数弹出效果
                this.createScorePopup(x, y, finalScore, false);

                // 如果是连击，创建连击弹出效果
                if (this.comboState.count > 1) {
                    this.createScorePopup(x, y - 40, this.comboState.count, true);
                }
            } else {
                // 如果centerTile无效，使用屏幕中心位置
                this.createScorePopup(this.app.displayWidth / 2, 300, finalScore, false);

                if (this.comboState.count > 1) {
                    this.createScorePopup(this.app.displayWidth / 2, 260, this.comboState.count, true);
                }
            }

            // 启动动画循环
            this.startAnimationLoop();
        });

        // 显示连击效果 - 修复layout依赖问题
        if (this.comboState.count > 1) {
            this.createScorePopup(
                this.app.displayWidth / 2,
                200, // 使用固定位置，避免layout依赖
                this.comboState.count,
                true
            );
        }

        // 更新分数
        this.gameState.score += totalScore;

        // 延迟后下落和重新检查
        setTimeout(() => {
            this.dropTiles();
            this.fillEmptyTiles();

            // 启动动画循环
            this.startAnimationLoop();

            // 等待下落动画完成后检查连锁反应
            this.waitForAnimations(() => {
                const newMatches = this.findMatches();
                if (newMatches.length > 0) {
                    this.processMatches(newMatches);
                } else {
                    this.gameState.isAnimating = false;
                    this.draw();
                }
            });
        }, 500);
    }

    /**
     * 方块下落（带动画）
     */
    dropTiles() {
        // 计算布局参数
        const displayWidth = this.app.displayWidth;
        const spacing_var = 15;
        const statsHeight = 100;
        const statsY = 120;
        const gameAreaWidth = displayWidth * 0.85;
        const gameAreaX = (displayWidth - gameAreaWidth) / 2;
        const gridPadding = 6;
        const gridSpacing = 2;
        const cols = CONFIG.board.cols;
        const tileSize = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
        const gridY = statsY + statsHeight + spacing_var;
        const gridX = gameAreaX;

        for (let col = 0; col < CONFIG.board.cols; col++) {
            // 从底部开始，将空位用上方的方块填充
            let writePos = CONFIG.board.rows - 1; // 写入位置

            for (let row = CONFIG.board.rows - 1; row >= 0; row--) {
                if (this.gameState.board[row] && this.gameState.board[row][col] && this.gameState.board[row][col].type >= 0) {
                    if (writePos !== row) {
                        // 创建下落动画
                        const startX = gridX + gridPadding + col * (tileSize + gridSpacing);
                        const startY = gridY + gridPadding + row * (tileSize + gridSpacing);
                        const targetY = gridY + gridPadding + writePos * (tileSize + gridSpacing);

                        this.animations.falling.push({
                            tile: { ...this.gameState.board[row][col] },
                            startX: startX,
                            startY: startY,
                            currentX: startX,
                            currentY: startY,
                            targetY: targetY,
                            targetRow: writePos,
                            targetCol: col,
                            tileSize: tileSize,
                            startTime: Date.now(),
                            duration: 200 + (writePos - row) * 30, // 下落距离越远，时间稍长
                            progress: 0
                        });

                        // 清空原位置
                        this.gameState.board[row][col] = { type: -1, selected: false };
                        // 目标位置暂时设为空，等动画完成后填充
                        this.gameState.board[writePos][col] = { type: -1, selected: false };
                    }
                    writePos--;
                }
            }
        }
    }

    /**
     * 填充空位（带动画，从顶部下落）
     */
    fillEmptyTiles() {
        // 计算布局参数
        const displayWidth = this.app.displayWidth;
        const spacing_var = 15;
        const statsHeight = 100;
        const statsY = 120;
        const gameAreaWidth = displayWidth * 0.85;
        const gameAreaX = (displayWidth - gameAreaWidth) / 2;
        const gridPadding = 6;
        const gridSpacing = 2;
        const cols = CONFIG.board.cols;
        const tileSize = (gameAreaWidth - gridPadding * 2 - gridSpacing * (cols - 1)) / cols;
        const gridY = statsY + statsHeight + spacing_var;
        const gridX = gameAreaX;

        // 从上到下填充空位，优先填充下方
        for (let col = 0; col < CONFIG.board.cols; col++) {
            let emptyCount = 0;
            let emptyPositions = [];

            // 找出这一列的所有空位，从下往上记录
            for (let row = CONFIG.board.rows - 1; row >= 0; row--) {
                if (!this.gameState.board[row] || !this.gameState.board[row][col] || this.gameState.board[row][col].type < 0) {
                    emptyPositions.push(row);
                    emptyCount++;
                }
            }

            // 为每个空位创建新方块和下落动画，优先填充下方
            emptyPositions.forEach((row, index) => {
                // 随机生成新方块
                const randomType = Math.floor(Math.random() * this.gameState.animalTypes);
                const newTile = {
                    type: randomType,
                    selected: false,
                    special: null,
                    row: row,
                    col: col
                };

                // 创建从顶部下落的动画
                const startX = gridX + gridPadding + col * (tileSize + gridSpacing);
                const startY = gridY - (emptyCount - index) * (tileSize + gridSpacing) - 50; // 从顶部更高的位置开始
                const targetY = gridY + gridPadding + row * (tileSize + gridSpacing);

                this.animations.falling.push({
                    tile: newTile,
                    startX: startX,
                    startY: startY,
                    currentX: startX,
                    currentY: startY,
                    targetY: targetY,
                    targetRow: row,
                    targetCol: col,
                    tileSize: tileSize,
                    startTime: Date.now() + index * 50, // 错开时间，创造连续下落效果
                    duration: 300 + index * 30,
                    progress: 0
                });

                // 目标位置暂时设为空，等动画完成后填充
                this.gameState.board[row][col] = { type: -1, selected: false };
            });
        }
    }

    /**
     * 重置连击状态
     */
    resetCombo() {
        this.comboState = {
            count: 0,
            multiplier: 1,
            lastMatchTime: 0
        };
    }

    /**
     * 检查道具点击
     */
    checkPropClick(x, y) {
        if (!this.propButtons) return false;

        for (let i = 0; i < this.propButtons.length; i++) {
            const prop = this.propButtons[i];

            // 跳过清屏卡的点击事件
            if (prop.type === 'clear') {
                continue;
            }

            if (x >= prop.x && x <= prop.x + prop.width &&
                y >= prop.y && y <= prop.y + prop.height) {

                if (prop.available && this.useProp(prop.type)) {
                    return true;
                }
            }
        }

        return false;
    }

    // checkPauseClick方法已删除

    // showPauseMenu方法已删除

    /**
     * 绘制菜单按钮
     */
    drawMenuButton(x, y, width, height, text, color) {
        const ctx = this.ctx;

        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.roundRect(x, y, width, height, height/2);
        ctx.fill();

        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x + width/2, y + height/2);
    }

    // bindPauseEvents方法已删除

    // resumeGame方法已删除

    /**
     * 重新开始游戏
     */
    restartGame() {
        this.gameState.score = 0;
        this.gameState.combo = 0;
        this.gameState.reviveCount = 0;
        this.gameState.props = {
            refresh: CONFIG.props.refresh.maxCount,
            bomb: CONFIG.props.bomb.maxCount,
            clear: CONFIG.props.clear.maxCount
        };
        this.initBoard();
        this.draw();
        this.bindEvents();
    }

    /**
     * 检查点是否在矩形内
     */
    isPointInRect(x, y, rectX, rectY, width, height) {
        return x >= rectX && x <= rectX + width &&
               y >= rectY && y <= rectY + height;
    }

    /**
     * 返回主页
     */
    back() {
        this.destroy();
        this.app.goBack();
    }

    /**
     * 销毁页面
     */
    destroy() {
        tt.offTouchStart();
        tt.offTouchMove();
        tt.offTouchEnd();
    }

    // 重复的触摸处理方法已删除，使用拖拽实现

    /**
     * 获取指定位置的方块
     */
    getTileAtPosition(x, y) {
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                const tile = this.gameState.board[row][col];
                if (tile &&
                    x >= tile.x && x <= tile.x + CONFIG.board.tileSize &&
                    y >= tile.y && y <= tile.y + CONFIG.board.tileSize) {
                    return tile;
                }
            }
        }
        return null;
    }

    /**
     * 检查是否相邻
     */
    isAdjacent(tile1, tile2) {
        const rowDiff = Math.abs(tile1.row - tile2.row);
        const colDiff = Math.abs(tile1.col - tile2.col);
        return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
    }

    // 重复的swapTiles方法已删除，使用坐标版本

    // 重复的processMatches方法已删除，使用带动画效果的版本

    /**
     * 播放匹配音效
     */
    playMatchSound() {
        if (this.gameState.combo >= 5) {
            this.app.playSound('combo5');
        } else if (this.gameState.combo >= 3) {
            this.app.playSound('combo3');
        } else {
            this.app.playSound('match');
        }
    }

    /**
     * 检查胜利条件
     */
    checkWinCondition() {
        if (this.gameState.score >= this.gameState.targetScore) {
            setTimeout(() => {
                this.endGame(true);
            }, 1000);
        }
    }

    /**
     * 查找匹配
     */
    // 重复的findMatches方法已删除，使用第一个版本

    /**
     * 移除匹配的方块
     */
    removeMatches(matches) {
        matches.forEach(match => {
            match.tiles.forEach(tile => {
                tile.type = -1; // 标记为已移除
            });
        });
    }

    /**
     * 生成特效方块
     */
    generateSpecialTiles(matches) {
        matches.forEach(match => {
            if (match.length === 4) {
                // 生成火箭
                const centerTile = match.tiles[Math.floor(match.length / 2)];
                centerTile.type = Math.floor(Math.random() * CONFIG.levels[this.gameState.level - 1].animalTypes);
                centerTile.effect = 'rocket';
            } else if (match.length >= 5) {
                // 生成炸弹
                const centerTile = match.tiles[Math.floor(match.length / 2)];
                centerTile.type = Math.floor(Math.random() * CONFIG.levels[this.gameState.level - 1].animalTypes);
                centerTile.effect = 'bomb';
            }
        });
    }

    /**
     * 方块下落
     */
    dropTiles() {
        for (let col = 0; col < CONFIG.board.cols; col++) {
            let writeRow = CONFIG.board.rows - 1;
            
            for (let row = CONFIG.board.rows - 1; row >= 0; row--) {
                if (this.gameState.board[row][col].type !== -1) {
                    if (row !== writeRow) {
                        this.gameState.board[writeRow][col] = { ...this.gameState.board[row][col] };
                        this.gameState.board[writeRow][col].row = writeRow;
                        this.gameState.board[writeRow][col].y = CONFIG.board.startY + writeRow * (CONFIG.board.tileSize + CONFIG.board.padding);
                    }
                    writeRow--;
                }
            }
            
            // 清空上方空位
            for (let row = writeRow; row >= 0; row--) {
                this.gameState.board[row][col] = {
                    type: -1,
                    x: CONFIG.board.startX + col * (CONFIG.board.tileSize + CONFIG.board.padding),
                    y: CONFIG.board.startY + row * (CONFIG.board.tileSize + CONFIG.board.padding),
                    row: row,
                    col: col,
                    selected: false,
                    effect: null
                };
            }
        }
    }

    /**
     * 填充空白位置
     */
    fillEmpty() {
        const levelConfig = CONFIG.levels[this.gameState.level - 1];
        
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                if (this.gameState.board[row][col].type === -1) {
                    this.gameState.board[row][col].type = Math.floor(Math.random() * levelConfig.animalTypes);
                }
            }
        }
    }

    /**
     * 处理无可用移动的情况
     */
    handleNoMoves() {
        // 检查是否还有免费复活次数
        const maxRevives = 2;
        if (!this.gameState.reviveCount) {
            this.gameState.reviveCount = 0;
        }

        if (this.gameState.reviveCount < maxRevives) {
            this.gameState.reviveCount++;
            this.shuffleBoard();
            this.app.playSound('match');

            // 显示复活提示
            this.showReviveMessage();
        } else {
            // 游戏结束
            this.endGame(false);
        }
    }

    /**
     * 显示复活提示
     */
    showReviveMessage() {
        const ctx = this.ctx;

        // 半透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 提示文字
        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.large}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('免费复活！', this.canvas.width / 2, this.canvas.height / 2);

        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText(`剩余复活次数：${2 - this.gameState.reviveCount}`,
                    this.canvas.width / 2, this.canvas.height / 2 + 40);

        // 2秒后消失
        setTimeout(() => {
            this.draw();
        }, 2000);
    }

    /**
     * 打乱棋盘
     */
    shuffleBoard() {
        const animals = [];
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                if (this.gameState.board[row][col].type !== -1) {
                    animals.push(this.gameState.board[row][col].type);
                }
            }
        }

        // Fisher-Yates洗牌算法
        for (let i = animals.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [animals[i], animals[j]] = [animals[j], animals[i]];
        }

        // 重新填充棋盘
        let index = 0;
        for (let row = 0; row < CONFIG.board.rows; row++) {
            for (let col = 0; col < CONFIG.board.cols; col++) {
                if (this.gameState.board[row][col].type !== -1) {
                    this.gameState.board[row][col].type = animals[index++];
                }
            }
        }

        // 确保有可行移动
        if (!this.hasValidMoves()) {
            this.shuffleBoard();
        }
    }

    /**
     * 结束游戏
     */
    endGame(isWin) {
        this.gameState.isAnimating = false;
        
        if (isWin) {
            this.app.playSound('win');
            // 通关逻辑
            if (this.gameState.level < CONFIG.levels.length) {
                this.app.globalData.currentLevel = this.gameState.level + 1;
            }
        } else {
            this.app.playSound('lose');
        }

        // 更新最高分
        if (this.gameState.score > this.app.globalData.highScore) {
            this.app.globalData.highScore = this.gameState.score;
        }

        // 保存数据
        this.app.saveGlobalData();

        // 显示结果
        setTimeout(() => {
            this.showResult(isWin);
        }, 1000);
    }

    /**
     * 显示游戏结果
     */
    showResult(isWin) {
        const ctx = this.ctx;

        // 半透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 结果背景卡片
        const cardWidth = 320;
        const cardHeight = 280;
        const cardX = (this.canvas.width - cardWidth) / 2;
        const cardY = (this.canvas.height - cardHeight) / 2;

        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.beginPath();
        ctx.roundRect(cardX, cardY, cardWidth, cardHeight, 16);
        ctx.fill();

        // 结果标题
        ctx.fillStyle = isWin ? CONFIG.ui.colors.primary : '#FF5722';
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.xxlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText(isWin ? '恭喜通关！' : '游戏结束', this.canvas.width / 2, cardY + 60);

        // 分数显示
        ctx.fillStyle = CONFIG.ui.colors.text;
        ctx.font = `bold ${CONFIG.ui.fonts.sizes.xlarge}px ${CONFIG.ui.fonts.primary}`;
        ctx.fillText(`最终分数: ${this.gameState.score}`, this.canvas.width / 2, cardY + 110);

        // 关卡信息
        if (isWin) {
            const levelConfig = CONFIG.levels[this.gameState.level - 1];
            ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
            ctx.fillText(`完成关卡: ${levelConfig.title}`, this.canvas.width / 2, cardY + 140);
        }

        // 按钮
        const buttonWidth = 120;
        const buttonHeight = 40;
        const button1X = this.canvas.width / 2 - buttonWidth - 10;
        const button2X = this.canvas.width / 2 + 10;
        const buttonY = cardY + 200;

        this.drawResultButton(button1X, buttonY, buttonWidth, buttonHeight, '重新开始', '#4CAF50');
        this.drawResultButton(button2X, buttonY, buttonWidth, buttonHeight, '返回主页', '#FF9800');

        // 绑定结果事件
        this.bindResultEvents(button1X, button2X, buttonY, buttonWidth, buttonHeight);
    }

    /**
     * 绘制结果按钮
     */
    drawResultButton(x, y, width, height, text, color) {
        const ctx = this.ctx;

        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.roundRect(x, y, width, height, height/2);
        ctx.fill();

        ctx.fillStyle = CONFIG.ui.colors.white;
        ctx.font = `${CONFIG.ui.fonts.sizes.medium}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x + width/2, y + height/2);
    }

    /**
     * 绑定结果事件
     */
    bindResultEvents(button1X, button2X, buttonY, buttonWidth, buttonHeight) {
        tt.offTouchStart();
        tt.onTouchStart((e) => {
            const touch = e.touches[0];
            const x = touch.clientX;
            const y = touch.clientY;

            if (this.isPointInRect(x, y, button1X, buttonY, buttonWidth, buttonHeight)) {
                // 重新开始
                this.app.playSound('match');
                this.restartGame();
            } else if (this.isPointInRect(x, y, button2X, buttonY, buttonWidth, buttonHeight)) {
                // 返回主页
                this.app.playSound('match');
                this.back();
            }
        });
    }

    /**
     * 更新动画（优化版）
     */
    updateAnimations() {
        const now = Date.now();

        // 优化：只有在有动画时才进行更新
        const hasAnimations = this.animations.falling.length > 0 ||
                             this.animations.explosions.length > 0 ||
                             this.animations.scorePopups.length > 0 ||
                             this.animations.comboPopups.length > 0;

        if (!hasAnimations) {
            return; // 没有动画，直接返回
        }

        // 更新下落动画
        if (this.animations.falling.length > 0) {
            this.animations.falling = this.animations.falling.filter(anim => {
                // 检查动画是否应该开始（考虑延迟开始时间）
                if (now < anim.startTime) {
                    return true; // 还没到开始时间，继续保留
                }

                anim.progress = Math.min(1, (now - anim.startTime) / anim.duration);
                anim.currentY = anim.startY + (anim.targetY - anim.startY) * this.easeOutBounce(anim.progress);

                if (anim.progress >= 1) {
                    // 动画完成，更新棋盘
                    this.gameState.board[anim.targetRow][anim.targetCol] = {
                        ...anim.tile,
                        row: anim.targetRow,
                        col: anim.targetCol
                    };
                    return false;
                }
                return true;
            });
        }

        // 更新爆炸效果（简化版）
        if (this.animations.explosions.length > 0) {
            this.animations.explosions = this.animations.explosions.filter(explosion => {
                explosion.progress = Math.min(1, (now - explosion.startTime) / explosion.duration);

                // 简化粒子更新
                if (explosion.progress < 1) {
                    explosion.particles.forEach(particle => {
                        particle.x += particle.vx * 0.8; // 减少移动速度
                        particle.y += particle.vy * 0.8;
                        particle.vy += 0.1; // 减少重力
                        particle.alpha = 1 - explosion.progress;
                    });
                    return true;
                }
                return false;
            });
        }

        // 更新分数弹出（优化版）
        if (this.animations.scorePopups.length > 0) {
            this.animations.scorePopups = this.animations.scorePopups.filter(popup => {
                popup.progress = Math.min(1, (now - popup.startTime) / popup.duration);

                if (popup.progress < 1) {
                    // 简化计算
                    if (!popup.startY) {
                        popup.startY = popup.y;
                    }
                    popup.y = popup.startY - (popup.progress * 40); // 减少移动距离
                    popup.alpha = 1 - popup.progress;
                    popup.scale = 1 + popup.progress * 0.3; // 减少缩放幅度
                    return true;
                }
                return false;
            });
        }

        // 更新连击弹出（优化版）
        if (this.animations.comboPopups.length > 0) {
            this.animations.comboPopups = this.animations.comboPopups.filter(popup => {
                popup.progress = Math.min(1, (now - popup.startTime) / popup.duration);

                if (popup.progress < 1) {
                    // 简化弹跳效果
                    popup.scale = 1 + Math.sin(popup.progress * Math.PI) * 0.2;
                    popup.alpha = 1 - popup.progress;
                    return true;
                }
                return false;
            });
        }
    }

    /**
     * 缓动函数 - 弹跳效果
     */
    easeOutBounce(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    }

    /**
     * 创建爆炸效果（优化版）
     */
    createExplosion(x, y, color) {
        const particles = [];
        // 减少粒子数量从8个到5个
        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI * 2;
            const speed = 2 + Math.random() * 1.5; // 减少速度范围
            particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                alpha: 1,
                color: color,
                size: 2 + Math.random() * 1.5 // 减少粒子大小
            });
        }

        this.animations.explosions.push({
            particles: particles,
            startTime: Date.now(),
            duration: 600, // 减少持续时间从800ms到600ms
            progress: 0
        });
    }

    /**
     * 创建分数弹出效果
     */
    createScorePopup(x, y, score, isCombo = false) {
        const popup = {
            x: x,
            y: y,
            score: score,
            startTime: Date.now(),
            duration: 1000,
            progress: 0,
            alpha: 1,
            scale: 1,
            isCombo: isCombo
        };

        if (isCombo) {
            this.animations.comboPopups.push(popup);
        } else {
            this.animations.scorePopups.push(popup);
        }
    }

    /**
     * 等待动画完成
     */
    waitForAnimations(callback) {
        const startTime = Date.now();
        const maxWaitTime = 5000; // 最多等待5秒

        const checkAnimations = () => {
            const elapsed = Date.now() - startTime;

            if (this.animations.falling.length === 0) {
                // 所有下落动画完成
                callback();
            } else if (elapsed > maxWaitTime) {
                // 超时保护，强制完成所有动画
                console.warn('动画等待超时，强制完成');
                this.animations.falling.forEach(anim => {
                    this.gameState.board[anim.targetRow][anim.targetCol] = {
                        ...anim.tile,
                        row: anim.targetRow,
                        col: anim.targetCol
                    };
                });
                this.animations.falling = [];
                callback();
            } else {
                // 继续等待
                setTimeout(checkAnimations, 100);
            }
        };

        // 开始检查
        setTimeout(checkAnimations, 100);
    }

    /**
     * 绘制动画效果（优化版）
     */
    drawAnimations() {
        const ctx = this.ctx;

        // 优化：批量处理相同类型的绘制

        // 绘制下落动画
        if (this.animations.falling.length > 0) {
            this.animations.falling.forEach(anim => {
                this.drawAnimatedTile(anim.tile, anim.currentX, anim.currentY, anim.tileSize);
            });
        }

        // 绘制爆炸效果（优化版）
        if (this.animations.explosions.length > 0) {
            ctx.save();
            this.animations.explosions.forEach(explosion => {
                explosion.particles.forEach(particle => {
                    if (particle.alpha > 0.1) { // 只绘制可见的粒子
                        ctx.globalAlpha = particle.alpha;
                        ctx.fillStyle = particle.color;
                        ctx.beginPath();
                        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                        ctx.fill();
                    }
                });
            });
            ctx.restore();
        }

        // 绘制分数弹出（优化版）
        if (this.animations.scorePopups.length > 0) {
            ctx.save();
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            this.animations.scorePopups.forEach(popup => {
                if (popup.alpha > 0.1) { // 只绘制可见的弹出
                    ctx.globalAlpha = popup.alpha;

                    // 简化字体设置
                    const fontSize = Math.round(20 * popup.scale);
                    ctx.font = `bold ${fontSize}px ${CONFIG.ui.fonts.primary}`;

                    // 简化绘制：只填充，不描边
                    ctx.fillStyle = '#FFD700';
                    ctx.fillText(`+${popup.score}`, popup.x, popup.y);
                }
            });
            ctx.restore();
        }

        // 绘制连击弹出（优化版）
        if (this.animations.comboPopups.length > 0) {
            ctx.save();
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            this.animations.comboPopups.forEach(popup => {
                if (popup.alpha > 0.1) { // 只绘制可见的弹出
                    ctx.globalAlpha = popup.alpha;

                    // 简化字体设置
                    const fontSize = Math.round(24 * popup.scale);
                    ctx.font = `bold ${fontSize}px ${CONFIG.ui.fonts.primary}`;

                    // 简化绘制：只填充，不描边
                    ctx.fillStyle = '#FF1744';
                    const comboText = `连击 x${this.comboState.count}!`;
                    ctx.fillText(comboText, popup.x, popup.y);
                }
            });
            ctx.restore();
        }
    }

    /**
     * 绘制动画中的方块
     */
    drawAnimatedTile(tile, x, y, size) {
        const ctx = this.ctx;
        const animal = CONFIG.animals.types[tile.type];
        if (!animal) return;

        // 方块背景
        ctx.save();
        ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 2;
        ctx.shadowBlur = 4;

        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.roundRect(x, y, size, size, 8);
        ctx.fill();
        ctx.restore();

        // 萌宠图片
        const imgSize = Math.round(size * 0.8);
        const imgX = Math.round(x + (size - imgSize) / 2);
        const imgY = Math.round(y + (size - imgSize) / 2);

        this.imageLoader.drawCrispImage(
            ctx,
            animal.id.toString(),
            imgX,
            imgY,
            imgSize,
            imgSize,
            animal.name
        );
    }

    /**
     * 启动计时器（优化版）
     */
    startTimer() {
        this.timerInterval = setInterval(() => {
            if (this.gameTimer.isRunning) {
                const newTime = Math.floor((Date.now() - this.gameTimer.startTime) / 1000);
                if (newTime !== this.gameTimer.currentTime) {
                    this.gameTimer.currentTime = newTime;
                    // 只在没有动画时重绘，避免与动画冲突
                    if (!this.gameState.isAnimating &&
                        this.animations.explosions.length === 0 &&
                        this.animations.scorePopups.length === 0 &&
                        this.animations.comboPopups.length === 0) {
                        this.draw();
                    }
                }
            }
        }, 1000); // 每秒更新一次
    }

    /**
     * 停止计时器
     */
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        this.gameTimer.isRunning = false;
    }

    /**
     * 紧急重置游戏状态（防止卡住）
     */
    emergencyReset() {
        console.log('执行紧急重置');

        // 重置动画状态
        this.gameState.isAnimating = false;

        // 清理所有动画
        this.animations.falling = [];
        this.animations.explosions = [];
        this.animations.scorePopups = [];
        this.animations.comboPopups = [];

        // 重置连击
        this.comboState.count = 0;
        this.comboState.multiplier = 1;

        // 重置拖拽状态
        this.resetDragState();

        // 重绘
        this.draw();
    }

    /**
     * 启动动画循环（修复版）
     */
    startAnimationLoop() {
        // 如果已经有动画循环在运行，先停止
        if (this.animationFrameId) {
            this.stopAnimationLoop();
        }

        const animationLoop = () => {
            // 检查是否有动画需要更新
            const hasAnimations = this.animations.falling.length > 0 ||
                                 this.animations.explosions.length > 0 ||
                                 this.animations.scorePopups.length > 0 ||
                                 this.animations.comboPopups.length > 0;

            if (hasAnimations) {
                this.draw(); // 只在有动画时重绘
                // 继续循环
                this.animationFrameId = requestAnimationFrame(animationLoop);
            } else {
                // 没有动画时停止循环
                this.animationFrameId = null;
            }
        };

        // 启动循环
        this.animationFrameId = requestAnimationFrame(animationLoop);
    }

    /**
     * 停止动画循环
     */
    stopAnimationLoop() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }


}

module.exports = GamePage;