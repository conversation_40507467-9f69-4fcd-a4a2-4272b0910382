/**
 * 萌宠爱消消 - 游戏配置文件
 * 包含所有游戏参数、资源路径、关卡配置等
 */

const CONFIG = {
    // 版本信息
    version: '1.0.0',
    versionTitle: '萌宠爱消消 v1.0.0',
    
    // 游戏基础配置
    game: {
        name: '萌宠爱消消',
        fps: 60,
        debug: false
    },
    
    // 关卡配置
    levels: [
        { 
            id: 1,
            title: '萌宠新手村',
            difficulty: '非常简单',
            targetScore: 1000, 
            animalTypes: 5 
        },
        { 
            id: 2,
            title: '萌宠大练兵',
            difficulty: '简单',
            targetScore: 4000, 
            animalTypes: 7 
        },
        { 
            id: 3,
            title: '萌宠总动员',
            difficulty: '超级困难',
            targetScore: 8000, 
            animalTypes: 9 
        }
    ],
    
    // 棋盘配置
    board: {
        rows:10,
        cols: 8,
        tileSize: 60,
        padding: 4,
        startX: 50,
        startY: 200
    },
    
    // 萌宠动物配置
    animals: {
        types: [
            { id: 9, name: '青蛙', emoji: '🐸', image: 'images/animal/frog.png' },
            { id: 1, name: '兔子', emoji: '🐰', image: 'images/animal/rabbit.png' },
            { id: 2, name: '猫咪', emoji: '🐱', image: 'images/animal/cat.png' },
            { id: 3, name: '小狗', emoji: '🐶', image: 'images/animal/dog.png' },
            { id: 4, name: '狐狸', emoji: '🦊', image: 'images/animal/fox.png' },
            { id: 5, name: '猴子', emoji: '🐵', image: 'images/animal/monkey.png' },
            { id: 6, name: '熊猫', emoji: '🐼', image: 'images/animal/panda.png' },
            { id: 7, name: '狮子', emoji: '🐘', image: 'images/animal/lion.png' },
            { id: 8, name: '老虎', emoji: '🐯', image: 'images/animal/tiger.png' }
        ]
    },
    
    // 得分系统配置
    score: {
        baseScore: 20,
        comboMultiplier: {
            1: 1.0,
            2: 1.0,
            3: 1.0,
            4: 1.5,
            5: 1.5,
            6: 2.0
        },
        specialEffects: {
            rocket: 100,
            bomb: 300,
            rocketCombo: 200,
            bombRocket: 400,
            bombCombo: 500
        }
    },
    
    // 道具配置
    props: {
        refresh: {
            name: '刷新卡',
            description: '打乱萌宠位置',
            maxCount: 2,
            image: 'images/prop/refresh.png',
            effect: 'shuffle'
        },
        bomb: {
            name: '炸弹卡',
            description: '5x5范围爆炸',
            maxCount: 1,
            image: 'images/prop/bomb.png',
            effect: 'bomb',
            score: 500
        },
        clear: {
            name: '清屏卡',
            description: '消除所有动物',
            maxCount: 0,
            image: 'images/prop/clear.png',
            effect: 'clear',
            score: 800
        }
    },
    
    // 资源路径配置
    resources: {
        animalImages: 'images/animal/',
        uiButtons: 'images/button/',
        uiIcons: 'images/icon/',
        props: 'images/prop/',
        audios: 'audios/',
        title: {
            pink: 'images/title.jpg',
            purple: 'images/title.png'
        }
    },
    
    // 音频配置
    audio: {
        background: 'audios/background.mp3',
        effects: {
            match: 'audios/so.mp3',
            rocket: 'audios/shua.mp3',
            bomb: 'audios/bomb.mp3',
            combo3: 'audios/wa.mp3',
            combo5: 'audios/good.mp3',
            win: 'audios/win.mp3',
            lose: 'audios/lose.mp3',
            cat: 'audios/cat.mp3'
        }
    },
    
    // 布局配置
    layout: {
        home: {
            title: { x: 0.5, y: 0.2 },
            buttons: { x: 0.5, y: 0.6 },
            spacing: 80
        },
        game: {
            score: { x: 30, y: 80 },
            target: { x: 30, y: 120 },
            props: { x: 0.5, y: 0.9 }
        }
    },
    
    // 存储键名配置
    storage: {
        highScore: 'pet_match_high_score',
        currentLevel: 'pet_match_current_level',
        coins: 'pet_match_coins',
        items: 'pet_match_items',
        settings: 'pet_match_settings',
        unlockedLevels: 'pet_match_unlocked_levels',
        achievements: 'pet_match_achievements'
    },
    
    // 动画配置
    animation: {
        matchDuration: 300,
        dropDuration: 400,
        explosionDuration: 500,
        fadeInDuration: 200,
        fadeOutDuration: 200
    },
    
    // UI配置
    ui: {
        colors: {
            primary: '#FF85A1',
            secondary: '#FFB6C1',
            accent: '#FFD1DC',
            text: '#333333',
            textLight: '#666666',
            background: '#F9F0FA',
            white: '#FFFFFF'
        },
        fonts: {
            primary: 'HarmonyOS Sans SC',
            sizes: {
                small: 12,
                medium: 16,
                large: 20,
                xlarge: 24,
                xxlarge: 36
            }
        },
        spacing: {
            small: 8,
            medium: 16,
            large: 24,
            xlarge: 32
        }
    },

    // 社交功能配置
    social: {
        share: {
            title: '萌宠爱消消',
            description: '超好玩的萌宠消除游戏，快来挑战吧！',
            imageUrl: 'images/share.png',
            reward: {
                type: 'bomb',
                count: 1
            }
        },
        invite: {
            title: '邀请好友一起玩萌宠爱消消',
            description: '邀请好友获得清屏卡奖励！',
            reward: {
                type: 'clear',
                count: 1
            }
        }
    }
};

module.exports = CONFIG;
