/**
 * 萌宠爱消消 - 主游戏入口
 * 抖音原生小游戏主文件
 */

const CONFIG = require('./config.js');
const BackgroundRenderer = require('./utils/BackgroundRenderer.js');
const ImageLoader = require('./utils/ImageLoader.js');
const UIComponents = require('./utils/UIComponents.js');
const CanvasUtils = require('./utils/CanvasUtils.js');
const AudioManager = require('./manager/audioManager.js');
const DataManager = require('./manager/dataManager.js');
const ThemeManager = require('./manager/themeManager.js');
const GamePage = require('./pages/GamePage.js');
const RankPage = require('./pages/RankPage.js');
const SettingPage = require('./pages/SettingPage.js');

class Game {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.currentPage = null;
        this.pageStack = [];

        // 管理器实例
        this.audioManager = null;
        this.dataManager = null;
        this.themeManager = null;

        // 全局数据
        this.globalData = {
            currentLevel: 1,
            highScore: 0,
            theme: 'default',
            settings: {}
        };

        // 动画相关
        this.stars = [];
        this.meteors = [];
        this.animationFrame = null;
        this.titleAnimation = {
            scale: 1,
            scaleDirection: 1,
            glow: 0,
            glowDirection: 1
        };

        // 图片加载器
        this.imageLoader = new ImageLoader();

        this.init();
    }

    /**
     * 初始化游戏
     */
    init() {
        console.log('萌宠爱消消游戏启动中...');

        // 创建画布
        this.createCanvas();

        // 初始化管理器
        this.initManagers();

        // 加载游戏数据
        this.loadGameData();

        // 显示主页
        this.showHomePage();

        console.log('游戏初始化完成');
    }



    /**
     * 创建游戏画布
     */
    createCanvas() {
        try {
            this.canvas = tt.createCanvas();
            this.ctx = this.canvas.getContext('2d');

            // 获取系统信息并动态计算画布尺寸
            const systemInfo = tt.getSystemInfoSync();

            // 获取设备像素比，确保高清显示
            const pixelRatio = systemInfo.pixelRatio || 1;

            // 计算实际显示尺寸
            const displayWidth = systemInfo.windowWidth;
            const displayHeight = systemInfo.windowHeight;

            // 设置画布的实际尺寸（考虑像素比）
            this.canvas.width = displayWidth * pixelRatio;
            this.canvas.height = displayHeight * pixelRatio;

            // 设置画布的显示尺寸
            this.canvas.style = this.canvas.style || {};
            this.canvas.style.width = displayWidth + 'px';
            this.canvas.style.height = displayHeight + 'px';

            // 缩放上下文以匹配设备像素比
            this.ctx.scale(pixelRatio, pixelRatio);

            // 优化Canvas渲染设置
            CanvasUtils.optimizeCanvasRendering(this.ctx);

            // 存储显示尺寸供后续使用
            this.displayWidth = displayWidth;
            this.displayHeight = displayHeight;
            this.pixelRatio = pixelRatio;

            console.log(`设备信息: ${displayWidth}x${displayHeight}, 像素比: ${pixelRatio}`);
            console.log(`画布尺寸: ${this.canvas.width}x${this.canvas.height}`);

            // 更新配置中的布局参数
            this.updateLayoutConfig();

        } catch (error) {
            console.error('创建画布失败:', error);
        }
    }

    /**
     * 更新布局配置
     */
    updateLayoutConfig() {
        // 动态计算棋盘布局
        const boardWidth = this.displayWidth * 0.9; // 棋盘占屏幕宽度90%
        const tileSize = Math.floor((boardWidth - 7 * CONFIG.board.padding) / CONFIG.board.cols);
        const boardHeight = CONFIG.board.rows * (tileSize + CONFIG.board.padding) - CONFIG.board.padding;

        // 更新配置
        CONFIG.board.tileSize = tileSize;
        CONFIG.board.startX = (this.displayWidth - (CONFIG.board.cols * (tileSize + CONFIG.board.padding) - CONFIG.board.padding)) / 2;
        CONFIG.board.startY = Math.max(150, (this.displayHeight - boardHeight) / 2 - 50);

        console.log(`动态布局: 方块大小=${tileSize}, 起始位置=(${CONFIG.board.startX}, ${CONFIG.board.startY})`);
    }

    /**
     * 初始化管理器
     */
    initManagers() {
        this.audioManager = new AudioManager();
        this.dataManager = new DataManager();
        this.themeManager = new ThemeManager();
    }

    /**
     * 加载游戏数据
     */
    loadGameData() {
        this.globalData.currentLevel = this.dataManager.getCurrentLevel();
        this.globalData.highScore = this.dataManager.getHighScore();
        this.globalData.theme = this.dataManager.getSettings().theme || 'default';
        this.globalData.settings = this.dataManager.getSettings();
    }

    /**
     * 保存游戏数据
     */
    saveGlobalData() {
        this.dataManager.setCurrentLevel(this.globalData.currentLevel);
        this.dataManager.setHighScore(this.globalData.highScore);
        this.dataManager.updateSettings(this.globalData.settings);
        this.dataManager.saveAllData();
    }

    /**
     * 显示主页
     */
    async showHomePage() {
        this.clearPage();
        this.currentPage = 'home';

        // 异步加载标题图片
        await this.loadTitleImage();

        this.initStars();
        this.drawHomePage();
        this.bindHomeEvents();
        this.startHomeAnimation();

        // 播放背景音乐
        this.audioManager.playBackgroundMusic();
    }

    /**
     * 绘制主页
     */
    drawHomePage() {
        const ctx = this.ctx;
        const theme = this.themeManager.getTheme(this.globalData.theme);

        // 清空画布
        ctx.clearRect(0, 0, this.displayWidth, this.displayHeight);

        // 绘制背景
        this.drawBackground(theme);

        // 绘制星星和流星动画
        this.drawStarsAndMeteors();

        // 绘制标题
        this.drawTitle(theme);

        // 绘制按钮
        this.drawHomeButtons(theme);

        // 绘制版权信息
        this.drawCopyright();
    }

    /**
     * 绘制背景
     */
    drawBackground(theme) {
        BackgroundRenderer.drawGradientBackground(this.ctx, this.displayWidth, this.displayHeight);
    }

    /**
     * 加载标题图片
     */
    async loadTitleImage() {
        try {
            await this.imageLoader.loadTitleImage();
            console.log('标题图片加载完成');
        } catch (error) {
            console.error('标题图片加载失败:', error);
        }
    }

    /**
     * 绘制标题（带动画效果）
     */
    drawTitle(theme) {
        const ctx = this.ctx;
        const titleY = this.displayHeight * 0.15;

        // 更新标题动画
        this.updateTitleAnimation();

        ctx.save();

        // 应用动画效果
        const centerX = this.displayWidth / 2;
        const centerY = titleY + 30;

        ctx.translate(centerX, centerY);
        ctx.scale(this.titleAnimation.scale, this.titleAnimation.scale);
        ctx.translate(-centerX, -centerY);

        // 添加发光效果
        ctx.shadowColor = CONFIG.ui.colors.primary;
        ctx.shadowBlur = this.titleAnimation.glow;

        // 使用ImageLoader绘制标题图片 - 使用整数坐标
        if (this.imageLoader.isLoaded('title')) {
            const imageWidth = Math.round(this.displayWidth * 0.8);
            const imageHeight = Math.round(imageWidth * 0.5); // 增加高度20 (从0.4到0.5)
            const imageX = Math.round((this.displayWidth - imageWidth) / 2);
            const imageY = Math.round(titleY);

            // 标题图片使用普通绘制方法（通常尺寸较大）
            this.imageLoader.drawImage(ctx, 'title', imageX, imageY, imageWidth, imageHeight, '萌宠爱消消');
        } else {
            // 备用文字标题
            const mainTitleSize = Math.min(36, this.displayWidth * 0.1);
            ctx.fillStyle = CONFIG.ui.colors.primary;
            ctx.font = `bold ${mainTitleSize}px ${CONFIG.ui.fonts.primary}`;
            ctx.textAlign = 'center';
            ctx.fillText('萌宠爱消消', this.displayWidth / 2, titleY + 40);
        }

        ctx.restore();
    }

    /**
     * 更新标题动画
     */
    updateTitleAnimation() {
        // 缩放动画
        this.titleAnimation.scale += this.titleAnimation.scaleDirection * 0.002;
        if (this.titleAnimation.scale > 1.05) {
            this.titleAnimation.scale = 1.05;
            this.titleAnimation.scaleDirection = -1;
        } else if (this.titleAnimation.scale < 0.95) {
            this.titleAnimation.scale = 0.95;
            this.titleAnimation.scaleDirection = 1;
        }

        // 发光动画
        this.titleAnimation.glow += this.titleAnimation.glowDirection * 0.5;
        if (this.titleAnimation.glow > 15) {
            this.titleAnimation.glow = 15;
            this.titleAnimation.glowDirection = -1;
        } else if (this.titleAnimation.glow < 5) {
            this.titleAnimation.glow = 5;
            this.titleAnimation.glowDirection = 1;
        }
    }

    /**
     * 绘制主页按钮
     */
    drawHomeButtons(theme) {
        const ctx = this.ctx;
        const centerX = this.displayWidth / 2;
        const startY = this.displayHeight * 0.6; // 向下调整按钮位置

        // 动态计算按钮尺寸 - 调小按钮，减少间距
        const buttonWidth = this.displayWidth * 0.6; // 从0.7减少到0.6
        const buttonHeight = Math.min(50, this.displayHeight * 0.07); // 从60减少到50
        const buttonSpacing = buttonHeight + 15; // 从25减少到15，按钮间距更紧凑

        // 按钮配置
        const buttons = [
            { text: '开始游戏', action: 'startGame' },
            { text: '排行榜', action: 'showRank' },
            { text: '设置', action: 'showSettings' }
        ];

        buttons.forEach((button, index) => {
            const y = startY + index * buttonSpacing;
            this.drawButton(centerX, y, button.text, CONFIG.ui.colors.primary, buttonWidth, buttonHeight);
        });

        // 存储按钮信息供点击检测使用
        this.homeButtons = buttons.map((button, index) => ({
            ...button,
            x: centerX - buttonWidth / 2,
            y: startY + index * buttonSpacing - buttonHeight / 2,
            width: buttonWidth,
            height: buttonHeight
        }));
    }

    /**
     * 绘制按钮
     */
    drawButton(x, y, text, color, width = 200, height = 60) {
        const ctx = this.ctx;
        const cornerRadius = height * 0.3;

        ctx.save();

        // 按钮底部阴影（立体感）
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 6;
        ctx.shadowBlur = 12;

        // 按钮主体渐变背景
        const gradient = ctx.createLinearGradient(
            x - width/2, y - height/2,
            x - width/2, y + height/2
        );
        gradient.addColorStop(0, this.lightenColor(color, 20));
        gradient.addColorStop(0.5, color);
        gradient.addColorStop(1, this.darkenColor(color, 20));

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.roundRect(x - width/2, y - height/2, width, height, cornerRadius);
        ctx.fill();

        // 按钮高光效果
        ctx.shadowColor = 'transparent';
        const highlightGradient = ctx.createLinearGradient(
            x - width/2, y - height/2,
            x - width/2, y - height/4
        );
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0.1)');

        ctx.fillStyle = highlightGradient;
        ctx.beginPath();
        ctx.roundRect(x - width/2, y - height/2, width, height/2, cornerRadius);
        ctx.fill();

        // 按钮边框
        ctx.strokeStyle = this.darkenColor(color, 30);
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(x - width/2, y - height/2, width, height, cornerRadius);
        ctx.stroke();

        // 按钮文字（带阴影）
        ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
        ctx.shadowBlur = 2;

        ctx.fillStyle = CONFIG.ui.colors.white;
        const fontSize = Math.min(height * 0.4, 24);
        ctx.font = `bold ${fontSize}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x, y);

        ctx.restore();
    }

    /**
     * 颜色加亮
     */
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    /**
     * 颜色加深
     */
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }

    /**
     * 绘制版权信息
     */
    drawCopyright() {
        const ctx = this.ctx;

        const copyrightSize = Math.min(12, this.displayWidth * 0.03); // 最大12px或屏幕宽度的3%
        ctx.fillStyle = CONFIG.ui.colors.textLight;
        ctx.font = `${copyrightSize}px ${CONFIG.ui.fonts.primary}`;
        ctx.textAlign = 'center';
        ctx.fillText('© 2023 萌宠爱消消 版权所有',
                    this.displayWidth / 2,
                    this.displayHeight - 30);
    }

    /**
     * 绑定主页事件
     */
    bindHomeEvents() {
        tt.offTouchStart(); // 清除之前的事件监听
        tt.onTouchStart(this.handleHomeTouch.bind(this));
    }

    /**
     * 处理主页触摸事件
     */
    handleHomeTouch(e) {
        const touch = e.touches[0];
        const x = touch.clientX;
        const y = touch.clientY;

        // 使用存储的按钮信息进行点击检测
        if (this.homeButtons) {
            for (let i = 0; i < this.homeButtons.length; i++) {
                const button = this.homeButtons[i];

                if (this.isPointInRect(x, y, button.x, button.y, button.width, button.height)) {
                    this.playSound('match');

                    switch (button.action) {
                        case 'startGame':
                            this.startGame();
                            break;
                        case 'showRank':
                            this.showRankPage();
                            break;
                        case 'showSettings':
                            this.showSettingPage();
                            break;
                    }
                    break;
                }
            }
        }
    }

    /**
     * 开始游戏
     */
    startGame() {
        this.clearPage();
        this.currentPage = 'game';
        this.pageStack.push('home');

        const gamePage = new GamePage(this);
        this.currentPageInstance = gamePage;
    }

    /**
     * 显示排行榜页面
     */
    showRankPage() {
        this.clearPage();
        this.currentPage = 'rank';
        this.pageStack.push('home');

        const rankPage = new RankPage(this);
        this.currentPageInstance = rankPage;
    }

    /**
     * 显示设置页面
     */
    showSettingPage() {
        this.clearPage();
        this.currentPage = 'setting';
        this.pageStack.push('home');

        const settingPage = new SettingPage(this);
        this.currentPageInstance = settingPage;
    }

    /**
     * 返回上一页
     */
    goBack() {
        if (this.pageStack.length > 0) {
            const previousPage = this.pageStack.pop();

            if (this.currentPageInstance && this.currentPageInstance.destroy) {
                this.currentPageInstance.destroy();
            }

            switch (previousPage) {
                case 'home':
                    this.showHomePage();
                    break;
                case 'game':
                    this.startGame();
                    break;
                case 'rank':
                    this.showRankPage();
                    break;
                case 'setting':
                    this.showSettingPage();
                    break;
            }
        }
    }

    /**
     * 清除当前页面
     */
    clearPage() {
        if (this.currentPageInstance && this.currentPageInstance.destroy) {
            this.currentPageInstance.destroy();
        }
        this.currentPageInstance = null;
        tt.offTouchStart();
        tt.offTouchMove();
        tt.offTouchEnd();
    }

    /**
     * 播放音效
     */
    playSound(soundName) {
        if (this.audioManager) {
            this.audioManager.playSound(soundName);
        }
    }

    /**
     * 检查点是否在矩形内
     */
    isPointInRect(x, y, rectX, rectY, width, height) {
        return x >= rectX && x <= rectX + width &&
               y >= rectY && y <= rectY + height;
    }

    /**
     * 游戏主循环
     */
    gameLoop() {
        if (this.currentPageInstance && this.currentPageInstance.update) {
            this.currentPageInstance.update();
        }

        if (this.currentPageInstance && this.currentPageInstance.draw) {
            this.currentPageInstance.draw();
        }

        requestAnimationFrame(this.gameLoop.bind(this));
    }

    /**
     * 初始化星星
     */
    initStars() {
        this.stars = [];
        for (let i = 0; i < 50; i++) {
            this.stars.push({
                x: Math.random() * this.displayWidth,
                y: Math.random() * this.displayHeight,
                size: Math.random() * 4 + 3, // 增大星星尺寸：3-7px
                opacity: Math.random() * 0.8 + 0.2,
                twinkleSpeed: Math.random() * 0.02 + 0.01,
                rotation: Math.random() * Math.PI * 2 // 随机旋转角度
            });
        }

        this.meteors = [];
    }

    /**
     * 开始主页动画
     */
    startHomeAnimation() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        this.animateHome();
    }

    /**
     * 主页动画循环
     */
    animateHome() {
        if (this.currentPage !== 'home') return;

        this.updateStars();
        this.updateMeteors();
        this.drawHomePage();

        this.animationFrame = requestAnimationFrame(this.animateHome.bind(this));
    }

    /**
     * 更新星星动画
     */
    updateStars() {
        this.stars.forEach(star => {
            star.opacity += star.twinkleSpeed;
            if (star.opacity > 1) {
                star.opacity = 1;
                star.twinkleSpeed = -Math.abs(star.twinkleSpeed);
            } else if (star.opacity < 0.2) {
                star.opacity = 0.2;
                star.twinkleSpeed = Math.abs(star.twinkleSpeed);
            }
        });
    }

    /**
     * 更新流星动画
     */
    updateMeteors() {
        // 增加流星生成频率
        if (Math.random() < 0.02) {
            this.meteors.push({
                x: Math.random() * this.displayWidth + 100, // 从更右上方开始
                y: -50, // 从更高处开始
                vx: -(Math.random() * 2 + 1.5), // 稍慢的水平速度
                vy: Math.random() * 3 + 4, // 更快的垂直速度，向下降落
                size: Math.random() * 3 + 2,
                life: 1.0,
                decay: Math.random() * 0.015 + 0.008,
                trail: []
            });
        }

        // 更新流星位置
        this.meteors = this.meteors.filter(meteor => {
            // 记录轨迹
            meteor.trail.push({ x: meteor.x, y: meteor.y, alpha: meteor.life });
            if (meteor.trail.length > 8) {
                meteor.trail.shift();
            }

            meteor.x += meteor.vx;
            meteor.y += meteor.vy;
            meteor.life -= meteor.decay;
            return meteor.life > 0 && meteor.y < this.displayHeight + 50 && meteor.x > -50;
        });
    }

    /**
     * 绘制星星和流星
     */
    drawStarsAndMeteors() {
        const ctx = this.ctx;

        // 绘制五角星 - 橘黄色
        this.stars.forEach(star => {
            ctx.save();
            ctx.globalAlpha = star.opacity;
            ctx.fillStyle = '#FF8C00'; // 橘黄色
            ctx.translate(star.x, star.y);
            ctx.rotate(star.rotation);
            BackgroundRenderer.drawStar(ctx, 0, 0, star.size);
            ctx.restore();
        });

        // 绘制流星（带拖尾效果）
        this.meteors.forEach(meteor => {
            ctx.save();

            // 绘制拖尾
            if (meteor.trail && meteor.trail.length > 1) {
                for (let i = 0; i < meteor.trail.length - 1; i++) {
                    const point = meteor.trail[i];
                    const nextPoint = meteor.trail[i + 1];
                    const alpha = (point.alpha * (i + 1)) / meteor.trail.length;

                    ctx.globalAlpha = alpha;
                    ctx.strokeStyle = '#FFD700'; // 金色流星
                    ctx.lineWidth = meteor.size * (alpha + 0.2);
                    ctx.beginPath();
                    ctx.moveTo(point.x, point.y);
                    ctx.lineTo(nextPoint.x, nextPoint.y);
                    ctx.stroke();
                }
            }

            // 绘制流星头部
            ctx.globalAlpha = meteor.life;
            ctx.fillStyle = '#FFFFFF'; // 白色流星头
            ctx.beginPath();
            ctx.arc(meteor.x, meteor.y, meteor.size, 0, 2 * Math.PI);
            ctx.fill();

            // 流星光晕
            ctx.globalAlpha = meteor.life * 0.3;
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(meteor.x, meteor.y, meteor.size * 2, 0, 2 * Math.PI);
            ctx.fill();

            ctx.restore();
        });
    }



    /**
     * 销毁游戏
     */
    destroy() {
        this.clearPage();

        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }

        if (this.audioManager) {
            this.audioManager.destroy();
        }

        console.log('游戏已销毁');
    }
}

// 启动游戏
const game = new Game();

module.exports = Game;