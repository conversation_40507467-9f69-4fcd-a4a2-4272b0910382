/**
 * GameUIConfig - 游戏UI配置类
 * 为游戏提供完整的UI配置管理
 */
class GameUIConfig {
    constructor(canvasWidth, canvasHeight) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // 统一颜色配置
        this.colors = {
            primary: 'rgba(255, 182, 193, 0.8)',    // 主要背景色 - 桃红色
            secondary: 'rgba(255, 255, 255, 0.9)',  // 次要背景色 - 白色
            text: '#FFFFFF',                         // 文字颜色
            border: 'rgba(255, 182, 193, 0.6)'      // 边框颜色
        };
        
        // 统一尺寸配置
        this.sizes = {
            borderRadius: 15,
            padding: 10,
            fontSize: 18
        };
        
        // 基础布局配置
        this.layout = {
            statsBar: {
                height: 60,
                yOffset: 20,
                widthPercent: 0.8
            },
            propBar: {
                height: 80,
                bottomOffset: 20,
                widthPercent: 0.8
            },
            grid: {
                spacing: 2,
                inset: 3,
                yOffset: 250,
                blockSize: 60
            },
            backButton: {
                x: 20,
                y: 75,
                width: 100,
                height: 40,
                text: '返回'
            }
        };
        
        // 道具初始数量配置
        this.propCounts = {
            refresh: 3,
            bomb: 2,
            clear: 1
        };
    }
    
    // 获取统计栏配置
    getStatsBarConfig() {
        const width = this.canvasWidth * this.layout.statsBar.widthPercent;
        const x = (this.canvasWidth - width) / 2;
        
        return {
            x: x,
            y: this.layout.statsBar.yOffset,
            width: width,
            height: this.layout.statsBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            textColor: this.colors.text,
            fontSize: this.sizes.fontSize,
            widthPercent: this.layout.statsBar.widthPercent
        };
    }
    
    // 获取网格配置
    getGridConfig() {
        const statsBarBottom = this.layout.statsBar.yOffset + this.layout.statsBar.height + 20;
        const propBarTop = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset - 20;
        const availableHeight = propBarTop - statsBarBottom;
        
        const gridSize = Math.min(this.canvasWidth * 0.9, availableHeight);
        const startX = (this.canvasWidth - gridSize) / 2;
        const startY = statsBarBottom + (availableHeight - gridSize) / 2;
        
        return {
            startX: startX,
            startY: startY,
            width: gridSize,
            height: gridSize,
            cols: 8,
            rows: 8,
            blockSize: this.layout.grid.blockSize || (gridSize / 8),
            padding: this.layout.grid.spacing,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            cellSpacing: this.layout.grid.spacing,
            cellInset: this.layout.grid.inset,
            cellBackgroundColor: this.colors.secondary,
            yOffset: this.layout.grid.yOffset
        };
    }
    
    // 获取道具栏配置
    getPropBarConfig() {
        const width = this.canvasWidth * this.layout.propBar.widthPercent;
        const x = (this.canvasWidth - width) / 2;
        const y = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset;
        
        return {
            x: x,
            y: y,
            width: width,
            height: this.layout.propBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            borderColor: this.colors.border,
            widthPercent: this.layout.propBar.widthPercent,
            propSpacing: 20
        };
    }
    
    // 获取返回按钮配置
    getBackButtonConfig() {
        return {
            x: this.layout.backButton.x,
            y: this.layout.backButton.y,
            width: this.layout.backButton.width,
            height: this.layout.backButton.height,
            text: this.layout.backButton.text,
            backgroundColor: this.colors.primary,
            textColor: this.colors.text,
            borderRadius: this.sizes.borderRadius,
            fontSize: this.sizes.fontSize
        };
    }
    
    // 获取道具初始数量 - 修复缺失的方法
    getPropInitialCount(propType) {
        return this.propCounts[propType] || 0;
    }
    
    // 动态更新配置
    updateConfig(section, property, value) {
        if (this.layout[section] && this.layout[section].hasOwnProperty(property)) {
            this.layout[section][property] = value;
            return true;
        }
        return false;
    }
    
    // 批量更新配置
    batchUpdateConfig(updates) {
        const results = {};
        for (const section in updates) {
            results[section] = {};
            for (const property in updates[section]) {
                results[section][property] = this.updateConfig(section, property, updates[section][property]);
            }
        }
        return results;
    }
    
    // 更新画布尺寸
    updateCanvasSize(width, height) {
        this.canvasWidth = width;
        this.canvasHeight = height;
    }
    
    // 获取配置摘要
    getConfigSummary() {
        return {
            canvasSize: {
                width: this.canvasWidth,
                height: this.canvasHeight
            },
            layout: this.layout,
            colors: this.colors,
            sizes: this.sizes,
            propCounts: this.propCounts
        };
    }
    
    // 获取动画配置
    getAnimationConfig() {
        return {
            clearFallSpeed: 6,
            bounceAmplitude: 10,
            bounceSpeed: 0.02
        };
    }
    
    // 获取图片配置
    getImageConfig() {
        return {
            enabled: true,
            basePath: 'images/',
            animals: 'images/animal/',
            props: 'images/prop/',
            extras: 'images/extra/',
            ui: 'images/ui/'
        };
    }
    
    // 获取动物图片路径
    getAnimalImagePath(type) {
        return `images/animal/${type}.png`;
    }
    
    // 获取道具图片路径
    getPropImagePath(type) {
        return `images/prop/${type}.png`;
    }
    
    // 获取额外图片路径
    getExtraImagePath(type) {
        return `images/extra/${type}.png`;
    }
    
    // 获取连击分数倍率
    getComboScoreMultiplier(comboCount) {
        const multipliers = [1, 1.2, 1.5, 2, 2.5, 3];
        return multipliers[Math.min(comboCount - 1, multipliers.length - 1)] || 3;
    }
    
    // 获取连击音效
    getComboAudio(comboCount) {
        if (comboCount >= 5) return 'good';
        if (comboCount >= 3) return 'so';
        return null;
    }
    
    // 获取交换分数
    getSwapScore(type1, type2) {
        const scores = {
            'normal-rocket': 120,
            'rocket-rocket': 200,
            'normal-bomb': 200,
            'rocket-bomb': 300,
            'bomb-bomb': 500
        };
        const key = `${type1}-${type2}`;
        return scores[key] || scores[`${type2}-${type1}`] || 100;
    }
    
    // 获取音频配置
    getAudioConfig() {
        return {
            enabled: true,
            volume: 0.5,
            basePath: 'audios/',
            sounds: {
                background: 'audios/background.mp3',
                bomb: 'audios/bomb.mp3',
                cat: 'audios/cat.mp3',
                good: 'audios/good.mp3',
                lose: 'audios/lose.mp3',
                shua: 'audios/shua.mp3',
                so: 'audios/so.mp3',
                wa: 'audios/wa.mp3',
                win: 'audios/win.mp3'
            }
        };
    }
    
    // 获取音频路径
    getAudioPath(soundName) {
        const audioConfig = this.getAudioConfig();
        return audioConfig.sounds[soundName] || `audios/${soundName}.mp3`;
    }

    // 获取游戏网格配置（兼容可爱风）
    getGameGridConfig() {
        return {
            cell: {
                spacing: this.layout.grid.spacing,
                inset: this.layout.grid.inset,
                backgroundColor: this.colors.secondary,
                borderRadius: this.sizes.borderRadius
            }
        };
    }

    // 获取主页按钮配置
    getMainPageButtonsConfig() {
        return {
            width: 200,
            height: 50,
            spacing: 15,
            backgroundColor: this.colors.primary,
            textColor: this.colors.text,
            borderRadius: this.sizes.borderRadius,
            fontSize: this.sizes.fontSize
        };
    }

    // 获取主页背景配置
    getMainPageBackgroundConfig() {
        return {
            gradient: {
                colors: [
                    '#FF69B4',  // 热粉色
                    '#DDA0DD',  // 梅花色
                    '#E6E6FA',  // 薰衣草色
                    '#FFB6C1',  // 浅粉色
                    '#DDA0DD'   // 梅花色
                ],
                direction: 'radial'
            }
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameUIConfig;
} else if (typeof window !== 'undefined') {
    window.GameUIConfig = GameUIConfig;
}
