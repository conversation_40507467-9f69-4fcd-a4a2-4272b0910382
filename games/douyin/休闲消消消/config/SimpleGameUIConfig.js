/**
 * 简化的游戏UI配置类
 * 删除所有冗余设计，只保留最基本的配置
 */
class SimpleGameUIConfig {
    constructor(canvasWidth, canvasHeight) {
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // 统一颜色配置 - 只需要修改这里
        this.colors = {
            primary: 'rgba(255, 182, 193, 0.8)',    // 主要背景色 - 桃红色
            secondary: 'rgba(255, 255, 255, 0.9)',  // 次要背景色 - 白色
            text: '#FFFFFF',                         // 文字颜色
            border: 'rgba(255, 182, 193, 0.6)'      // 边框颜色
        };
        
        // 统一尺寸配置
        this.sizes = {
            borderRadius: 15,
            padding: 10,
            fontSize: 18
        };
        
        // 基础布局配置
        this.layout = {
            statsBar: {
                height: 60,
                yOffset: 20
            },
            propBar: {
                height: 80,
                bottomOffset: 20
            },
            grid: {
                spacing: 2,
                inset: 3
            }
        };
    }
    
    // 获取统计栏配置
    getStatsBarConfig() {
        const width = this.canvasWidth * 0.9;
        const x = (this.canvasWidth - width) / 2;
        
        return {
            x: x,
            y: this.layout.statsBar.yOffset,
            width: width,
            height: this.layout.statsBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            textColor: this.colors.text,
            fontSize: this.sizes.fontSize
        };
    }
    
    // 获取网格配置
    getGridConfig() {
        const statsBarBottom = this.layout.statsBar.yOffset + this.layout.statsBar.height + 20;
        const propBarTop = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset - 20;
        const availableHeight = propBarTop - statsBarBottom;
        
        const gridSize = Math.min(this.canvasWidth * 0.9, availableHeight);
        const startX = (this.canvasWidth - gridSize) / 2;
        const startY = statsBarBottom + (availableHeight - gridSize) / 2;
        
        return {
            startX: startX,
            startY: startY,
            width: gridSize,
            height: gridSize,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            cellSpacing: this.layout.grid.spacing,
            cellInset: this.layout.grid.inset,
            cellBackgroundColor: this.colors.secondary
        };
    }
    
    // 获取道具栏配置
    getPropBarConfig() {
        const width = this.canvasWidth * 0.9;
        const x = (this.canvasWidth - width) / 2;
        const y = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset;
        
        return {
            x: x,
            y: y,
            width: width,
            height: this.layout.propBar.height,
            backgroundColor: this.colors.primary,
            borderRadius: this.sizes.borderRadius,
            borderColor: this.colors.border
        };
    }
    
    // 获取主页按钮配置
    getMainPageButtonsConfig() {
        return {
            width: 200,
            height: 50,
            spacing: 15,
            backgroundColor: this.colors.primary,
            textColor: this.colors.text,
            borderRadius: this.sizes.borderRadius,
            fontSize: this.sizes.fontSize
        };
    }
    
    // 计算主页按钮位置
    calculateMainPageButtonPositions(canvasWidth, canvasHeight) {
        const config = this.getMainPageButtonsConfig();
        const centerX = canvasWidth / 2;
        const startY = canvasHeight / 2 + 50;
        
        return [
            {
                x: centerX - config.width / 2,
                y: startY,
                width: config.width,
                height: config.height
            },
            {
                x: centerX - config.width / 2,
                y: startY + config.height + config.spacing,
                width: config.width,
                height: config.height
            },
            {
                x: centerX - config.width / 2,
                y: startY + (config.height + config.spacing) * 2,
                width: config.width,
                height: config.height
            }
        ];
    }
    
    // 获取主页背景配置
    getMainPageBackgroundConfig() {
        return {
            gradient: {
                colors: [
                    '#FF69B4',  // 热粉色
                    '#DDA0DD',  // 梅花色
                    '#E6E6FA',  // 薰衣草色
                    '#FFB6C1',  // 浅粉色
                    '#DDA0DD'   // 梅花色
                ],
                direction: 'radial'
            }
        };
    }

    // 兼容 GamePageCore 的方法 - 重写以避免递归
    getGridConfig() {
        const statsBarBottom = this.layout.statsBar.yOffset + this.layout.statsBar.height + 20;
        const propBarTop = this.canvasHeight - this.layout.propBar.height - this.layout.propBar.bottomOffset - 20;
        const availableHeight = propBarTop - statsBarBottom;

        const gridSize = Math.min(this.canvasWidth * 0.9, availableHeight);
        const startX = (this.canvasWidth - gridSize) / 2;
        const startY = statsBarBottom + (availableHeight - gridSize) / 2;

        return {
            startX: startX,
            startY: startY,
            width: gridSize,
            height: gridSize,
            cols: 8,
            rows: 8,
            blockSize: gridSize / 8,
            padding: this.layout.grid.spacing
        };
    }

    // 获取道具初始数量
    getPropInitialCount(propType) {
        const counts = {
            refresh: 3,
            bomb: 2,
            clear: 1
        };
        return counts[propType] || 0;
    }

    // 获取动画配置
    getAnimationConfig() {
        return {
            clearFallSpeed: 6
        };
    }

    // 获取图片配置
    getImageConfig() {
        return true; // 简化版本，表示有图片配置
    }

    // 获取动物图片路径
    getAnimalImagePath(type) {
        return `images/animal/${type}.png`;
    }

    // 获取额外图片路径
    getExtraImagePath(type) {
        return `images/extra/${type}.png`;
    }

    // 获取道具图片路径
    getPropImagePath(type) {
        return `images/prop/${type}.png`;
    }

    // 获取连击分数倍率
    getComboScoreMultiplier(comboCount) {
        const multipliers = [1, 1.2, 1.5, 2, 2.5, 3];
        return multipliers[Math.min(comboCount - 1, multipliers.length - 1)] || 3;
    }

    // 获取连击音效
    getComboAudio(comboCount) {
        if (comboCount >= 5) return 'good';
        if (comboCount >= 3) return 'so';
        return null;
    }

    // 获取交换分数
    getSwapScore(type1, type2) {
        const scores = {
            'normal-rocket': 120,
            'rocket-rocket': 200,
            'normal-bomb': 200,
            'rocket-bomb': 300,
            'bomb-bomb': 500
        };
        const key = `${type1}-${type2}`;
        return scores[key] || scores[`${type2}-${type1}`] || 100;
    }

    // 获取返回按钮配置
    getBackButtonConfig() {
        return {
            x: 20,
            y: 75,
            width: 100,
            height: 40
        };
    }

    // 获取标题图片配置
    getTitleImageConfig() {
        return {
            enabled: true,
            path: 'images/title.png',
            width: 300,
            height: 100,
            x: this.canvasWidth / 2 - 150,
            y: 100,
            animation: {
                enabled: true,
                type: 'bounce',
                speed: 0.02
            }
        };
    }

    // 获取音频配置
    getAudioConfig() {
        return {
            enabled: true,
            volume: 0.5,
            sounds: {
                click: 'sounds/click.mp3',
                match: 'sounds/match.mp3',
                combo: 'sounds/combo.mp3',
                good: 'sounds/good.mp3',
                so: 'sounds/so.mp3'
            }
        };
    }

    // 获取背景元素配置
    getBackgroundElementsConfig() {
        return {
            enabled: true,
            particles: {
                enabled: true,
                count: 20,
                color: this.colors.primary
            },
            decorations: {
                enabled: true,
                hearts: true,
                stars: true
            }
        };
    }

    // 获取游戏网格配置（兼容可爱风）
    getGameGridConfig() {
        return {
            cell: {
                spacing: this.layout.grid.spacing,
                inset: this.layout.grid.inset,
                backgroundColor: this.colors.secondary,
                borderRadius: this.sizes.borderRadius
            }
        };
    }

    // 获取音频路径
    getAudioPath(soundName) {
        const audioConfig = this.getAudioConfig();
        return audioConfig.sounds[soundName] || `sounds/${soundName}.mp3`;
    }

    // 获取图片路径配置
    getImagePathConfig() {
        return {
            animals: 'images/animals/',
            props: 'images/props/',
            ui: 'images/ui/'
        };
    }

    // 获取等级配置
    getLevelConfig() {
        return {
            maxLevel: 10,
            scoreThresholds: [100, 300, 600, 1000, 1500, 2100, 2800, 3600, 4500, 5500]
        };
    }

    // 计算标题图片位置
    calculateTitleImagePosition(canvasWidth, canvasHeight, titleConfig) {
        const width = titleConfig ? titleConfig.width : 300;
        const height = titleConfig ? titleConfig.height : 100;

        return {
            x: canvasWidth / 2 - width / 2,
            y: canvasHeight * 0.2, // 距离顶部20%的位置
            width: width,
            height: height
        };
    }

    // 计算标题图片尺寸
    calculateTitleImageSize(canvasWidth, imageWidth, imageHeight) {
        const maxWidth = canvasWidth * 0.6;
        const maxHeight = 120;

        let finalWidth = imageWidth;
        let finalHeight = imageHeight;

        // 按宽度缩放
        if (finalWidth > maxWidth) {
            const ratio = maxWidth / finalWidth;
            finalWidth = maxWidth;
            finalHeight = finalHeight * ratio;
        }

        // 按高度缩放
        if (finalHeight > maxHeight) {
            const ratio = maxHeight / finalHeight;
            finalHeight = maxHeight;
            finalWidth = finalWidth * ratio;
        }

        return {
            width: finalWidth,
            height: finalHeight,
            x: canvasWidth / 2 - finalWidth / 2,
            y: canvasWidth * 0.15 // 距离顶部15%的位置
        };
    }

    // 获取主页标题配置
    getMainPageTitleConfig() {
        return {
            enabled: true,
            text: "消消乐游戏",
            fontSize: 48,
            color: '#FFFFFF',
            fontFamily: 'Arial, "Microsoft YaHei"',
            shadow: {
                enabled: true,
                color: 'rgba(0, 0, 0, 0.5)',
                blur: 4,
                offsetX: 2,
                offsetY: 2
            },
            animation: {
                enabled: true,
                type: 'bounce',
                amplitude: 10,
                speed: 0.02
            }
        };
    }

    // 获取粒子系统配置
    getParticleSystemConfig() {
        return {
            enabled: true,
            maxParticles: 50,
            colors: [this.colors.primary, '#FFD700', '#FF69B4'],
            size: { min: 2, max: 6 },
            speed: { min: 1, max: 3 },
            life: { min: 2, max: 4 }
        };
    }

    // 获取文字标题配置
    getTextTitleConfig() {
        return {
            text: "消消乐游戏",
            fontSize: 48,
            color: '#FFFFFF',
            fontFamily: 'Arial, "Microsoft YaHei"',
            shadowColor: 'rgba(0, 0, 0, 0.5)',
            shadowBlur: 4,
            shadowOffsetX: 2,
            shadowOffsetY: 2
        };
    }

    // 获取副标题配置
    getSubtitleConfig() {
        return {
            text: "点击开始游戏",
            fontSize: 24,
            color: 'rgba(255, 255, 255, 0.8)',
            fontFamily: 'Arial, "Microsoft YaHei"'
        };
    }

    // 计算副标题位置
    calculateSubtitlePosition(canvasWidth, canvasHeight) {
        return {
            x: canvasWidth / 2,
            y: canvasHeight / 3 + 80
        };
    }

    // 获取版本信息配置
    getVersionInfoConfig() {
        return {
            enabled: true,
            text: "v1.0.0",
            fontSize: 14,
            color: 'rgba(255, 255, 255, 0.7)',
            position: { x: 20, y: 30 }
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleGameUIConfig;
} else if (typeof window !== 'undefined') {
    window.SimpleGameUIConfig = SimpleGameUIConfig;
}
