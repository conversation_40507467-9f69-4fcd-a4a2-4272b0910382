/**
 * 游戏管理器类
 * 负责画布管理、模块加载和页面切换
 */
class GameManager {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.currentPage = null;
        this.gameConfig = null;  // 游戏配置
        this.gameData = {
            settings: null,  // 将从配置文件加载
            playerData: {
                bestScore: 0,
                currentLevel: 1,
                totalGames: 0
            },
            rankData: []
        };

        // 模块缓存
        this.loadedModules = {};

        // 音效管理器
        this.audioManager = null;

        this.init();
    }

    init() {
        console.log('初始化萌宠爱消消游戏...');
        this.createCanvas();
        this.loadGameConfig()
            .then(() => this.loadGameData())
            .then(() => this.loadCoreModules())
            .then(() => this.switchToPage('main'))
            .then(() => {
                this.startGameLoop();
                console.log('游戏管理器初始化完成');
            })
            .catch(error => {
                console.error('游戏初始化失败:', error);
                // 即使初始化失败，也尝试启动基本的主页面
                this.createBeautifulMainPage();
                this.startGameLoop();
            });
    }

    createCanvas() {
        this.canvas = tt.createCanvas();
        this.ctx = this.canvas.getContext('2d');
        const systemInfo = tt.getSystemInfoSync();
        this.canvas.width = systemInfo.windowWidth;
        this.canvas.height = systemInfo.windowHeight;

        console.log(`画布尺寸: ${this.canvas.width} x ${this.canvas.height}`);
    }

    loadGameConfig() {
        return new Promise((resolve) => {
            try {
                // 尝试加载配置文件
                if (typeof GAME_CONFIG !== 'undefined') {
                    this.gameConfig = GAME_CONFIG;
                    console.log('从全局变量加载游戏配置成功');
                } else {
                    // 如果没有全局配置，使用默认配置
                    this.gameConfig = {
                        GAME_NAME: '休闲消消消',
                        SUBTITLE: '萌宠消除大作战',
                        DEFAULT_SETTINGS: {
                            bgmVolume: 0.5,
                            effectVolume: 0.5,
                            mute: false,
                            muteMode: false,
                            autoSave: true,
                            showTips: true
                        },
                        COLORS: {
                            PRIMARY: '#FF85A1',
                            SECONDARY: '#FFB6C1',
                            ACCENT: '#FFD1DC',
                            TEXT_PRIMARY: '#FFFFFF'
                        }
                    };
                    console.log('使用默认游戏配置');
                }
                resolve();
            } catch (error) {
                console.error('加载游戏配置失败:', error);
                // 使用最基本的默认配置
                this.gameConfig = {
                    GAME_NAME: '休闲消消消',
                    SUBTITLE: '萌宠消除大作战',
                    DEFAULT_SETTINGS: {
                        bgmVolume: 0.5,
                        effectVolume: 0.5,
                        mute: false,
                        muteMode: false,
                        autoSave: true,
                        showTips: true
                    }
                };
                resolve();
            }
        });
    }

    loadGameData() {
        return new Promise((resolve) => {
            try {
                // 从配置文件获取默认设置
                const defaultSettings = this.gameConfig.DEFAULT_SETTINGS || {
                    bgmVolume: 0.5,
                    effectVolume: 0.5,
                    mute: false,
                    muteMode: false,
                    autoSave: true,
                    showTips: true
                };

                const savedSettings = tt.getStorageSync('gameSettings');
                if (savedSettings) {
                    this.gameData.settings = { ...defaultSettings, ...JSON.parse(savedSettings) };
                    console.log('从缓存加载用户设置，合并默认配置');
                } else {
                    // 如果没有缓存设置，使用配置文件中的默认设置
                    this.gameData.settings = { ...defaultSettings };

                    // 保存默认设置到缓存
                    tt.setStorageSync('gameSettings', JSON.stringify(defaultSettings));
                    console.log('使用配置文件默认设置并写入缓存');
                }

                const savedPlayerData = tt.getStorageSync('playerData');
                if (savedPlayerData) {
                    this.gameData.playerData = { ...this.gameData.playerData, ...JSON.parse(savedPlayerData) };
                }
                console.log('游戏数据加载完成');
                resolve();
            } catch (error) {
                console.error('加载游戏数据失败:', error);
                // 即使失败也要确保有默认设置
                this.gameData.settings = {
                    bgmVolume: 0.5,
                    effectVolume: 0.5,
                    mute: false,
                    muteMode: false,
                    autoSave: true,
                    showTips: true
                };
                resolve();
            }
        });
    }

    loadCoreModules() {
        return new Promise((resolve) => {
            try {
                // 加载音效管理器
                this.loadAudioManager();
                console.log('核心模块加载完成');
                resolve();
            } catch (error) {
                console.error('加载核心模块失败:', error);
                resolve(); // 即使失败也继续
            }
        });
    }

    loadAudioManager() {
        try {
            this.audioManager = new AudioManager(this.gameData.settings);
            console.log('音效管理器加载成功');
        } catch (error) {
            console.error('音效管理器加载失败:', error);
            // 创建一个简单的音效管理器替代
            this.audioManager = {
                playSound: (name) => console.log(`播放音效: ${name}`),
                playBGM: () => console.log('播放背景音乐'),
                stopBGM: () => console.log('停止背景音乐'),
                setVolume: (volume) => console.log(`设置音量: ${volume}`)
            };
        }
    }

    switchToPage(pageName, options = {}) {
        return new Promise((resolve, reject) => {
            try {
                console.log(`切换到页面: ${pageName}`);
                
                // 清理当前页面
                if (this.currentPage && this.currentPage.destroy) {
                    this.currentPage.destroy();
                }

                // 根据页面名称创建对应的页面实例
                switch (pageName) {
                    case 'main':
                        this.loadMainPage(options).then(resolve).catch(reject);
                        break;
                    case 'game':
                        this.loadGamePage(options).then(resolve).catch(reject);
                        break;
                    case 'settings':
                        this.loadSettingsPage(options).then(resolve).catch(reject);
                        break;
                    case 'rank':
                        this.loadRankPage(options).then(resolve).catch(reject);
                        break;
                    default:
                        console.warn(`未知页面: ${pageName}`);
                        this.createBeautifulMainPage();
                        resolve();
                }
            } catch (error) {
                console.error(`切换页面失败: ${pageName}`, error);
                reject(error);
            }
        });
    }

    loadMainPage(options) {
        return new Promise((resolve, reject) => {
            try {
                if (typeof MainPage !== 'undefined') {
                    this.currentPage = new MainPage(this, this.canvas, this.ctx, options);
                    console.log('主页面加载成功');
                    resolve();
                } else {
                    console.warn('MainPage 类未找到，创建美化主页面');
                    this.createBeautifulMainPage();
                    resolve();
                }
            } catch (error) {
                console.error('主页面加载失败:', error);
                this.createBeautifulMainPage();
                resolve();
            }
        });
    }

    loadGamePage(options) {
        return new Promise((resolve, reject) => {
            try {
                if (typeof GamePage !== 'undefined') {
                    this.currentPage = new GamePage(this, this.canvas, this.ctx, options);
                    console.log('游戏页面加载成功');
                    resolve();
                } else {
                    console.error('GamePage 类未找到');
                    reject(new Error('GamePage 类未找到'));
                }
            } catch (error) {
                console.error('游戏页面加载失败:', error);
                reject(error);
            }
        });
    }

    loadSettingsPage(options) {
        return new Promise((resolve, reject) => {
            try {
                if (typeof SettingsPage !== 'undefined') {
                    this.currentPage = new SettingsPage(this, this.canvas, this.ctx, options);
                    console.log('设置页面加载成功');
                    resolve();
                } else {
                    console.warn('SettingsPage 类未找到，创建简单设置页面');
                    this.createSimpleSettingsPage();
                    resolve();
                }
            } catch (error) {
                console.error('设置页面加载失败:', error);
                this.createSimpleSettingsPage();
                resolve();
            }
        });
    }

    loadRankPage(options) {
        return new Promise((resolve, reject) => {
            try {
                if (typeof RankPage !== 'undefined') {
                    this.currentPage = new RankPage(this, this.canvas, this.ctx, options);
                    console.log('排行榜页面加载成功');
                    resolve();
                } else {
                    console.warn('RankPage 类未找到，创建简单排行榜页面');
                    this.createSimpleRankPage();
                    resolve();
                }
            } catch (error) {
                console.error('排行榜页面加载失败:', error);
                this.createSimpleRankPage();
                resolve();
            }
        });
    }

    createBeautifulMainPage() {
        console.log('创建美化主页面');
        this.currentPage = {
            render: () => {
                const ctx = this.ctx;
                const canvas = this.canvas;

                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 从配置文件获取颜色信息
                const colors = this.gameConfig.COLORS || {
                    PRIMARY: '#FF85A1',
                    SECONDARY: '#FFB6C1',
                    ACCENT: '#FFD1DC',
                    TEXT_PRIMARY: '#FFFFFF'
                };

                // 绘制渐变背景
                const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
                gradient.addColorStop(0, '#FFE5F1');
                gradient.addColorStop(0.5, colors.SECONDARY || '#FFB6C1');
                gradient.addColorStop(1, colors.PRIMARY || '#FF69B4');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 从配置文件获取游戏名称
                const gameName = this.gameConfig.GAME_NAME || '萌宠爱消消';
                const subtitle = this.gameConfig.SUBTITLE || '萌宠消除大作战';

                // 绘制标题
                ctx.fillStyle = colors.TEXT_PRIMARY || '#FFFFFF';
                ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
                ctx.textAlign = 'center';
                ctx.fillText(gameName, canvas.width / 2, canvas.height / 3);

                // 绘制副标题
                ctx.font = '24px Arial, "Microsoft YaHei"';
                ctx.fillText(subtitle, canvas.width / 2, canvas.height / 3 + 60);
                
                // 绘制开始游戏按钮
                const buttonWidth = 200;
                const buttonHeight = 60;
                const buttonX = (canvas.width - buttonWidth) / 2;
                const buttonY = canvas.height / 2;
                
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);
                
                ctx.fillStyle = '#FF69B4';
                ctx.font = 'bold 24px Arial';
                ctx.fillText('开始游戏', canvas.width / 2, buttonY + 38);
            },
            
            handleTouch: (x, y) => {
                const buttonWidth = 200;
                const buttonHeight = 60;
                const buttonX = (this.canvas.width - buttonWidth) / 2;
                const buttonY = this.canvas.height / 2;
                
                if (x >= buttonX && x <= buttonX + buttonWidth && 
                    y >= buttonY && y <= buttonY + buttonHeight) {
                    console.log('点击开始游戏');
                    this.switchToPage('game');
                }
            },
            
            update: () => {},
            destroy: () => {}
        };
    }

    createSimpleSettingsPage() {
        console.log('创建简单设置页面');
        this.currentPage = {
            render: () => {
                const ctx = this.ctx;
                const canvas = this.canvas;

                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景
                ctx.fillStyle = '#F0F0F0';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.fillStyle = '#333333';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('游戏设置', canvas.width / 2, 100);

                // 绘制返回按钮
                ctx.fillStyle = '#FF69B4';
                ctx.fillRect(20, 20, 80, 40);
                ctx.fillStyle = '#FFFFFF';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('返回', 60, 45);
            },

            handleTouch: (x, y) => {
                if (x >= 20 && x <= 100 && y >= 20 && y <= 60) {
                    this.switchToPage('main');
                }
            },

            update: () => {},
            destroy: () => {}
        };
    }

    createSimpleRankPage() {
        console.log('创建简单排行榜页面');
        this.currentPage = {
            render: () => {
                const ctx = this.ctx;
                const canvas = this.canvas;

                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景
                ctx.fillStyle = '#F0F0F0';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.fillStyle = '#333333';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('排行榜', canvas.width / 2, 100);

                // 绘制返回按钮
                ctx.fillStyle = '#FF69B4';
                ctx.fillRect(20, 20, 80, 40);
                ctx.fillStyle = '#FFFFFF';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('返回', 60, 45);
            },

            handleTouch: (x, y) => {
                if (x >= 20 && x <= 100 && y >= 20 && y <= 60) {
                    this.switchToPage('main');
                }
            },

            update: () => {},
            destroy: () => {}
        };
    }

    startGameLoop() {
        const gameLoop = () => {
            try {
                if (this.currentPage) {
                    if (this.currentPage.update) {
                        this.currentPage.update();
                    }
                    if (this.currentPage.render) {
                        this.currentPage.render();
                    }
                }
            } catch (error) {
                console.error('游戏循环错误:', error);
            }

            requestAnimationFrame(gameLoop);
        };

        gameLoop();
        console.log('游戏循环已启动');
    }

    saveGameData() {
        try {
            tt.setStorageSync('gameSettings', JSON.stringify(this.gameData.settings));
            tt.setStorageSync('playerData', JSON.stringify(this.gameData.playerData));
            console.log('游戏数据保存成功');
        } catch (error) {
            console.error('保存游戏数据失败:', error);
        }
    }

    updatePlayerData(data) {
        this.gameData.playerData = { ...this.gameData.playerData, ...data };
        if (this.gameData.settings.autoSave) {
            this.saveGameData();
        }
    }

    updateSettings(settings) {
        this.gameData.settings = { ...this.gameData.settings, ...settings };
        if (this.audioManager) {
            this.audioManager.updateSettings(this.gameData.settings);
        }
        this.saveGameData();
    }

    getGameData() {
        return this.gameData;
    }

    // 处理触摸事件
    handleTouch(x, y) {
        if (this.currentPage && this.currentPage.handleTouch) {
            this.currentPage.handleTouch(x, y);
        }
    }

    // 处理触摸开始事件
    handleTouchStart(x, y) {
        if (this.currentPage && this.currentPage.handleTouchStart) {
            this.currentPage.handleTouchStart(x, y);
        }
    }

    // 处理触摸结束事件
    handleTouchEnd(x, y) {
        if (this.currentPage && this.currentPage.handleTouchEnd) {
            this.currentPage.handleTouchEnd(x, y);
        }
    }

    // 处理触摸移动事件
    handleTouchMove(x, y) {
        if (this.currentPage && this.currentPage.handleTouchMove) {
            this.currentPage.handleTouchMove(x, y);
        }
    }

    // 销毁游戏管理器
    destroy() {
        if (this.currentPage && this.currentPage.destroy) {
            this.currentPage.destroy();
        }

        if (this.audioManager && this.audioManager.destroy) {
            this.audioManager.destroy();
        }

        this.saveGameData();
        console.log('游戏管理器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameManager;
} else {
    window.GameManager = GameManager;
}
