/**
 * 火箭交换调试测试
 * 帮助诊断火箭交换后没有消除列的问题
 */

// 测试火箭交换调试
function testRocketSwapDebug() {
    console.log('=== 火箭交换调试测试 ===');
    
    console.log('问题描述:');
    console.log('- 小火箭可以和相邻格子交换');
    console.log('- 但是交换后没有消除火箭所在的列');
    
    console.log('\n可能的原因:');
    console.log('1. 火箭交换条件检查失败');
    console.log('2. checkPositionForMatch 返回 false');
    console.log('3. eliminateColumn 没有正确执行');
    console.log('4. 事件处理器阻止了消除效果');
    console.log('5. 火箭格子属性设置不正确');
    
    return {
        problem: '火箭交换后没有消除列',
        possibleCauses: [
            '交换条件检查失败',
            'checkPositionForMatch 返回 false',
            'eliminateColumn 执行失败',
            '事件处理器问题',
            '格子属性错误'
        ]
    };
}

// 模拟火箭交换流程
function simulateRocketSwapFlow() {
    console.log('\n=== 模拟火箭交换流程 ===');
    
    // 模拟火箭和普通格子
    const rocketBlock = {
        type: 'rocket',
        blockType: 'special',
        special: 'rocket',
        row: 5,
        col: 3
    };
    
    const normalBlock = {
        type: 'cat',
        blockType: 'normal',
        special: null,
        row: 5,
        col: 4
    };
    
    console.log('交换前状态:');
    console.log(`- 火箭: (${rocketBlock.row},${rocketBlock.col}) type=${rocketBlock.type}, special=${rocketBlock.special}`);
    console.log(`- 普通: (${normalBlock.row},${normalBlock.col}) type=${normalBlock.type}, special=${normalBlock.special}`);
    
    // 模拟交换条件检查
    const type1 = rocketBlock.blockType;
    const type2 = normalBlock.blockType;
    
    const isRocketSwap = (type1 === 'normal' && normalBlock.special === 'rocket') ||
                        (type2 === 'normal' && rocketBlock.special === 'rocket');
    
    console.log(`\n交换条件检查:`);
    console.log(`- type1=${type1}, type2=${type2}`);
    console.log(`- 条件1: ${type1 === 'normal'} && ${normalBlock.special === 'rocket'} = ${type1 === 'normal' && normalBlock.special === 'rocket'}`);
    console.log(`- 条件2: ${type2 === 'normal'} && ${rocketBlock.special === 'rocket'} = ${type2 === 'normal' && rocketBlock.special === 'rocket'}`);
    console.log(`- 是否火箭交换: ${isRocketSwap}`);
    
    if (isRocketSwap) {
        console.log('\n✅ 火箭交换条件满足');
        
        // 模拟交换后的位置
        const normalNewRow = type1 === 'normal' ? rocketBlock.row : normalBlock.row;
        const normalNewCol = type1 === 'normal' ? rocketBlock.col : normalBlock.col;
        const rocketNewRow = type1 === 'normal' ? normalBlock.row : rocketBlock.row;
        const rocketNewCol = type1 === 'normal' ? normalBlock.col : rocketBlock.col;
        
        console.log(`交换后位置:`);
        console.log(`- 普通格子新位置: (${normalNewRow},${normalNewCol})`);
        console.log(`- 火箭新位置: (${rocketNewRow},${rocketNewCol})`);
        
        // 模拟匹配检查
        console.log(`\n匹配检查:`);
        console.log(`- 检查普通格子新位置 (${normalNewRow},${normalNewCol}) 是否有匹配`);
        console.log(`- 假设有匹配: true`);
        
        // 模拟消除
        console.log(`\n消除执行:`);
        console.log(`- 调用 eliminateColumn(${rocketNewCol})`);
        console.log(`- 应该消除火箭新位置的整列`);
        
        return {
            success: true,
            rocketNewCol,
            eliminatedCol: rocketNewCol
        };
    } else {
        console.log('\n❌ 火箭交换条件不满足');
        return {
            success: false,
            reason: '交换条件检查失败'
        };
    }
}

// 检查火箭格子属性
function checkRocketBlockProperties() {
    console.log('\n=== 火箭格子属性检查 ===');
    
    console.log('正确的火箭格子属性应该是:');
    console.log('- type: "rocket"');
    console.log('- blockType: "special"');
    console.log('- special: "rocket"');
    
    console.log('\n正确的普通格子属性应该是:');
    console.log('- type: "cat" (或其他动物类型)');
    console.log('- blockType: "normal"');
    console.log('- special: null 或 undefined');
    
    // 模拟格子创建
    const mockRocket = {
        type: 'rocket',
        blockType: 'special',
        special: 'rocket',
        animalType: null,
        color: '#FF4500'
    };
    
    const mockNormal = {
        type: 'cat',
        blockType: 'normal',
        special: null,
        animalType: 'cat',
        color: '#FF6B9D'
    };
    
    console.log('\n模拟格子属性:');
    console.log('火箭格子:', mockRocket);
    console.log('普通格子:', mockNormal);
    
    // 验证属性
    const rocketValid = mockRocket.type === 'rocket' && 
                       mockRocket.blockType === 'special' && 
                       mockRocket.special === 'rocket';
    
    const normalValid = mockNormal.blockType === 'normal' && 
                       !mockNormal.special;
    
    console.log(`\n属性验证:`);
    console.log(`- 火箭格子属性正确: ${rocketValid ? '✅' : '❌'}`);
    console.log(`- 普通格子属性正确: ${normalValid ? '✅' : '❌'}`);
    
    return { rocketValid, normalValid, mockRocket, mockNormal };
}

// 调试交换条件
function debugSwapConditions() {
    console.log('\n=== 交换条件调试 ===');
    
    const propertyCheck = checkRocketBlockProperties();
    
    if (!propertyCheck.rocketValid || !propertyCheck.normalValid) {
        console.log('❌ 格子属性设置有问题');
        return false;
    }
    
    // 模拟 processSpecialSwap 中的条件检查
    const block1 = propertyCheck.mockRocket;
    const block2 = propertyCheck.mockNormal;
    
    const type1 = block1.blockType;
    const type2 = block2.blockType;
    
    console.log(`processSpecialSwap 条件检查:`);
    console.log(`- block1: type=${block1.type}, blockType=${type1}, special=${block1.special}`);
    console.log(`- block2: type=${block2.type}, blockType=${type2}, special=${block2.special}`);
    
    // 检查火箭交换条件
    const condition1 = (type1 === 'normal' && block2.special === 'rocket');
    const condition2 = (type2 === 'normal' && block1.special === 'rocket');
    
    console.log(`\n条件检查详细:`);
    console.log(`- 条件1: (${type1} === 'normal') && (${block2.special} === 'rocket') = ${condition1}`);
    console.log(`- 条件2: (${type2} === 'normal') && (${block1.special} === 'rocket') = ${condition2}`);
    console.log(`- 最终结果: ${condition1 || condition2}`);
    
    if (condition1 || condition2) {
        console.log('✅ 交换条件满足，应该执行火箭交换');
        return true;
    } else {
        console.log('❌ 交换条件不满足，不会执行火箭交换');
        return false;
    }
}

// 调试消除执行
function debugEliminateExecution() {
    console.log('\n=== 消除执行调试 ===');
    
    console.log('eliminateColumn 方法应该执行以下步骤:');
    console.log('1. 遍历指定列的所有行');
    console.log('2. 对每个非空格子:');
    console.log('   - 添加到 eliminatedBlocks 数组');
    console.log('   - 添加粒子效果');
    console.log('   - 设置 this.grid[row][col] = null');
    console.log('3. 返回 eliminatedBlocks 数组');
    
    // 模拟消除执行
    const col = 4;
    const gridSizeY = 10;
    const mockGrid = [];
    
    // 创建模拟网格
    for (let row = 0; row < gridSizeY; row++) {
        mockGrid[row] = [];
        for (let c = 0; c < 8; c++) {
            if (c === col) {
                mockGrid[row][c] = { type: 'cat', color: '#FF6B9D' };
            } else {
                mockGrid[row][c] = { type: 'dog', color: '#4ECDC4' };
            }
        }
    }
    
    console.log(`\n模拟消除列${col}:`);
    const eliminatedBlocks = [];
    
    for (let row = 0; row < gridSizeY; row++) {
        const block = mockGrid[row][col];
        if (block) {
            eliminatedBlocks.push({ row, col, block });
            console.log(`  消除格子(${row},${col}): ${block.type}`);
            mockGrid[row][col] = null; // 清除格子
        }
    }
    
    console.log(`消除完成: 清除了${eliminatedBlocks.length}个格子`);
    
    // 验证消除后的状态
    console.log(`\n消除后列${col}状态:`);
    for (let row = 0; row < gridSizeY; row++) {
        const block = mockGrid[row][col];
        console.log(`  (${row},${col}): ${block ? block.type : 'null'}`);
    }
    
    return {
        eliminatedCount: eliminatedBlocks.length,
        allCleared: eliminatedBlocks.length === gridSizeY
    };
}

// 综合调试分析
function comprehensiveDebugAnalysis() {
    console.log('\n=== 综合调试分析 ===');
    
    const swapFlow = simulateRocketSwapFlow();
    const conditionCheck = debugSwapConditions();
    const eliminateCheck = debugEliminateExecution();
    
    console.log('调试结果汇总:');
    console.log(`- 交换流程模拟: ${swapFlow.success ? '✅' : '❌'}`);
    console.log(`- 交换条件检查: ${conditionCheck ? '✅' : '❌'}`);
    console.log(`- 消除执行模拟: ${eliminateCheck.allCleared ? '✅' : '❌'}`);
    
    if (swapFlow.success && conditionCheck && eliminateCheck.allCleared) {
        console.log('\n🤔 理论上应该工作正常，可能的问题:');
        console.log('1. 实际游戏中的格子属性与预期不符');
        console.log('2. checkPositionForMatch 在实际场景中返回 false');
        console.log('3. 事件处理器中的 isRocketSwap 判断有误');
        console.log('4. eliminateColumn 的实际执行被其他代码干扰');
        
        console.log('\n🔍 建议的调试步骤:');
        console.log('1. 在 processSpecialSwap 开始处添加日志');
        console.log('2. 在火箭交换条件检查处添加详细日志');
        console.log('3. 在 checkPositionForMatch 中添加日志');
        console.log('4. 在 eliminateColumn 中添加详细日志');
        console.log('5. 在事件处理器中验证 isRocketSwap 判断');
    } else {
        console.log('\n❌ 发现问题:');
        if (!swapFlow.success) console.log('- 交换流程有问题');
        if (!conditionCheck) console.log('- 交换条件检查失败');
        if (!eliminateCheck.allCleared) console.log('- 消除执行有问题');
    }
    
    return {
        swapFlow,
        conditionCheck,
        eliminateCheck,
        overallStatus: swapFlow.success && conditionCheck && eliminateCheck.allCleared
    };
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        testRocketSwapDebug, 
        simulateRocketSwapFlow, 
        checkRocketBlockProperties,
        debugSwapConditions,
        debugEliminateExecution,
        comprehensiveDebugAnalysis 
    };
} else {
    window.testRocketSwapDebug = testRocketSwapDebug;
    window.simulateRocketSwapFlow = simulateRocketSwapFlow;
    window.checkRocketBlockProperties = checkRocketBlockProperties;
    window.debugSwapConditions = debugSwapConditions;
    window.debugEliminateExecution = debugEliminateExecution;
    window.comprehensiveDebugAnalysis = comprehensiveDebugAnalysis;
}
