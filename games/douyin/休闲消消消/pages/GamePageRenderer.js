/**
 * 游戏页面渲染器 - 简洁美化版本
 * 负责所有视觉效果的渲染，无浮动动画
 */
class GamePageRenderer {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        
        // 动画时间
        this.animationTime = 0;
        
        // 根据关卡配置颜色主题
        this.initLevelColors();

        // 使用全局配置的颜色
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};
        this.colors = {
            background: this.levelColors,
            grid: config.COLORS ? {
                background: config.COLORS.GRID_BACKGROUND,
                border: config.COLORS.GRID_BORDER,
                shadow: config.COLORS.GRID_SHADOW
            } : {
                background: 'rgba(255, 255, 255, 0.8)',
                border: '#FFFFFF',
                shadow: 'rgba(0, 0, 0, 0.1)'
            },
            animals: config.COLORS ? config.COLORS.ANIMALS : {
                'cat': { bg: '#FF6B9D', glow: '#FF8FB3' },
                'dog': { bg: '#4ECDC4', glow: '#6ED5CD' },
                'elephant': { bg: '#45B7D1', glow: '#65C7E1' },
                'fox': { bg: '#96CEB4', glow: '#A6DEC4' },
                'frog': { bg: '#FFEAA7', glow: '#FFEFB7' },
                'monkey': { bg: '#DDA0DD', glow: '#E7B0E7' },
                'panda': { bg: '#98D8C8', glow: '#A8E8D8' },
                'rabbit': { bg: '#F7DC6F', glow: '#F9E67F' },
                'tiger': { bg: '#BB8FCE', glow: '#CB9FDE' }
            },
            special: config.COLORS ? config.COLORS.SPECIAL : {
                'rocket': { bg: '#FF4500', glow: '#FF6347' },
                'bomb': { bg: '#8B0000', glow: '#DC143C' }
            }
        };
        
        console.log('GamePageRenderer初始化完成');
    }

    // 根据关卡初始化颜色主题（使用全局配置）
    initLevelColors() {
        const level = this.core.level || 1;
        const config = typeof GAME_CONFIG !== 'undefined' ? GAME_CONFIG : {};

        // 使用全局配置的关卡主题颜色
        if (config.COLORS && config.COLORS.LEVEL_THEMES) {
            this.levelColors = config.COLORS.LEVEL_THEMES[level] || config.COLORS.LEVEL_THEMES[1];
        } else {
            // 回退到默认主题
            switch (level) {
                case 1:
                    this.levelColors = {
                        primary: '#E8F5E8',
                        secondary: '#B8E6B8',
                        accent: '#87CEEB',
                        gradient: ['#E8F5E8', '#B8E6B8', '#87CEEB', '#98FB98']
                    };
                    break;
                case 2:
                    this.levelColors = {
                        primary: '#F0E6FF',
                        secondary: '#DDA0DD',
                        accent: '#9370DB',
                        gradient: ['#F0E6FF', '#DDA0DD', '#9370DB', '#BA55D3']
                    };
                    break;
                case 3:
                default:
                    this.levelColors = {
                        primary: '#FFE5F1',
                        secondary: '#FFB6C1',
                        accent: '#FF69B4',
                        gradient: ['#FFE5F1', '#FFB6C1', '#FF69B4', '#FF1493']
                    };
                    break;
            }
        }

        console.log(`关卡${level}使用颜色主题:`, this.levelColors);
    }
    
    // 主渲染方法
    render() {
        this.animationTime += 0.016; // 60fps
        
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 渲染背景
        this.renderBackground();
        
        // 渲染UI界面
        this.renderUI();
        
        // 渲染游戏网格
        this.renderGrid();

        // 渲染道具栏
        this.renderPropBar();

        // 渲染特效
        this.renderEffects();

        // 渲染游戏状态
        this.renderGameStatus();

        // 渲染退出确认对话框
        if (this.core.showExitDialog) {
            this.renderExitDialog();
        }

        // 渲染炸弹卡拖拽
        if (this.core.events && this.core.events.isDraggingBomb) {
            this.renderBombDrag();
        }
    }
    
    // 渲染梦幻背景 - 支持关卡颜色
    renderBackground() {
        // 主背景渐变 - 使用关卡颜色
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2,
            Math.max(this.canvas.width, this.canvas.height) / 2
        );

        const colors = this.levelColors.gradient;
        colors.forEach((color, index) => {
            gradient.addColorStop(index / (colors.length - 1), color);
        });

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 添加静态装饰
        this.renderStaticDecorations();
    }
    
    // 渲染静态装饰
    renderStaticDecorations() {
        // 添加一些静态的几何装饰
        for (let i = 0; i < 6; i++) {
            const x = (this.canvas.width / 6) * i + 50;
            const y = 40;
            
            this.ctx.save();
            this.ctx.globalAlpha = 0.3;
            
            // 绘制小圆点装饰
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.beginPath();
            this.ctx.arc(x, y, 3, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 绘制小星形装饰
            if (i % 2 === 0) {
                this.ctx.fillStyle = '#FFD700';
                this.ctx.translate(x, y + 15);
                this.ctx.beginPath();
                for (let j = 0; j < 5; j++) {
                    const angle = (j * Math.PI * 2) / 5;
                    const radius = j % 2 === 0 ? 6 : 3;
                    const px = Math.cos(angle) * radius;
                    const py = Math.sin(angle) * radius;
                    if (j === 0) {
                        this.ctx.moveTo(px, py);
                    } else {
                        this.ctx.lineTo(px, py);
                    }
                }
                this.ctx.closePath();
                this.ctx.fill();
            }
            
            this.ctx.restore();
        }
    }
    
    // 渲染UI界面 - 使用新的布局配置
    renderUI() {
        // 使用新的布局配置
        let statsBarConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            statsBarConfig = CONFIG_UTILS.getStatsBarConfig(this.canvas.width, this.canvas.height);
        } else {
            // 回退到默认配置
            statsBarConfig = {
                x: this.canvas.width * 0.05,
                y: 70,
                width: this.canvas.width * 0.9,
                height: 120
            };
        }

        // 顶部信息栏背景
        const headerX = statsBarConfig.x;
        const headerY = statsBarConfig.y;
        const headerWidth = statsBarConfig.width;
        const headerHeight = statsBarConfig.height;

        const headerGradient = this.ctx.createLinearGradient(headerX, headerY, headerX, headerY + headerHeight);
        headerGradient.addColorStop(0, 'rgba(135, 206, 250, 0.95)'); // 天蓝色
        headerGradient.addColorStop(1, 'rgba(70, 130, 180, 0.8)'); // 钢蓝色

        // 统计栏背景（圆角）
        this.ctx.fillStyle = headerGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(headerX, headerY, headerWidth, headerHeight, 12);
        this.ctx.fill();

        // 添加装饰边框（圆角）
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(headerX + 5, headerY + 5, headerWidth - 10, headerHeight - 10, 8);
        this.ctx.stroke();

        // 关卡名称
        this.ctx.fillStyle = '#FF1493';
        this.ctx.font = 'bold 28px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 3;
        this.ctx.strokeText(this.core.levelName, this.canvas.width / 2, headerY + 30);
        this.ctx.fillText(this.core.levelName, this.canvas.width / 2, headerY + 30);

        // 分3个模块显示：分数、目标、进度
        const moduleWidth = (this.canvas.width - 60) / 3;
        const moduleY = headerY + 75;

        // 分数模块 - 使用全局变量
        this.ctx.fillStyle = '#4169E1';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.strokeText(`分数`, 30 + moduleWidth * 0.5, moduleY - 15);
        this.ctx.fillText(`分数`, 30 + moduleWidth * 0.5, moduleY - 15);
        this.ctx.strokeText(`${window.gameScore || 0}`, 30 + moduleWidth * 0.5, moduleY + 10);
        this.ctx.fillText(`${window.gameScore || 0}`, 30 + moduleWidth * 0.5, moduleY + 10);

        // 目标模块 - 使用全局变量
        this.ctx.fillStyle = '#32CD32';
        this.ctx.strokeText(`目标`, 30 + moduleWidth * 1.5, moduleY - 15);
        this.ctx.fillText(`目标`, 30 + moduleWidth * 1.5, moduleY - 15);
        this.ctx.strokeText(`${window.gameTargetScore || 0}`, 30 + moduleWidth * 1.5, moduleY + 10);
        this.ctx.fillText(`${window.gameTargetScore || 0}`, 30 + moduleWidth * 1.5, moduleY + 10);

        // 进度模块 - 使用全局变量
        const progressPercent = Math.round((window.gameProgress || 0) * 100);
        this.ctx.fillStyle = '#FF69B4';
        this.ctx.strokeText(`进度`, 30 + moduleWidth * 2.5, moduleY - 15);
        this.ctx.fillText(`进度`, 30 + moduleWidth * 2.5, moduleY - 15);
        this.ctx.strokeText(`${progressPercent}%`, 30 + moduleWidth * 2.5, moduleY + 10);
        this.ctx.fillText(`${progressPercent}%`, 30 + moduleWidth * 2.5, moduleY + 10);

        // 返回按钮（使用新的布局配置）
        let backButtonConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            const layoutCalc = CONFIG_UTILS.calculateLayout(this.canvas.width, this.canvas.height);
            backButtonConfig = layoutCalc.backButton;
        } else {
            backButtonConfig = { x: 20, y: 30, width: 100, height: 32 };
        }
        this.renderBackButton(backButtonConfig.x, backButtonConfig.y, backButtonConfig.width, backButtonConfig.height, '返回');
    }

    // 移除了进度条渲染方法

    // 渲染道具栏（使用新的布局配置）
    renderPropBar() {
        // 使用新的布局配置
        let propsBarConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            propsBarConfig = CONFIG_UTILS.getPropsBarConfig(this.canvas.width, this.canvas.height);
        } else {
            // 回退到默认配置
            const gridBottom = this.core.gridStartY + this.core.gridSizeY * this.core.blockSize;
            propsBarConfig = {
                x: this.canvas.width * 0.05,
                y: gridBottom + 10,
                width: this.canvas.width * 0.9,
                height: 80,
                buttonSize: 60,
                buttonSpacing: 20
            };
        }

        const barX = propsBarConfig.x;
        const propBarY = propsBarConfig.y;
        const barWidth = propsBarConfig.width;
        const barHeight = propsBarConfig.height;
        const propSize = propsBarConfig.buttonSize;

        // 道具布局计算（支持配置化）
        const layoutMode = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.LAYOUT_MODE) ?
                          GAME_CONFIG.LAYOUT.PROPS_BAR.LAYOUT_MODE : 'EQUAL_SPLIT';
        const propCount = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.PROP_COUNT) ?
                         GAME_CONFIG.LAYOUT.PROPS_BAR.PROP_COUNT : 3;

        let startX, propSpacing;

        if (layoutMode === 'EQUAL_SPLIT') {
            // 平分布局：道具栏宽度平分成N份
            const sectionWidth = barWidth / propCount;
            propSpacing = sectionWidth;
            startX = barX + sectionWidth / 2; // 每个区域的中心点

            console.log(`道具栏平分布局: 总宽度${barWidth}px, ${propCount}个道具, 每份${sectionWidth.toFixed(1)}px`);
        } else {
            // 传统间距布局
            const buttonSpacing = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_SPACING) ?
                                 GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_SPACING : 20;
            const totalPropsWidth = propCount * propSize + (propCount - 1) * buttonSpacing;
            startX = barX + (barWidth - totalPropsWidth) / 2;
            propSpacing = propSize + buttonSpacing;
        }

        // 美化的渐变背景（浅色调，添加圆角）
        const propGradient = this.ctx.createLinearGradient(barX, propBarY, barX, propBarY + barHeight);
        propGradient.addColorStop(0, 'rgba(135, 206, 250, 0.7)'); // 浅天蓝色
        propGradient.addColorStop(1, 'rgba(100, 149, 237, 0.6)'); // 浅蓝色

        this.ctx.fillStyle = propGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(barX, propBarY, barWidth, barHeight, 12);
        this.ctx.fill();

        // 美化的边框（圆角）
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.roundRect(barX, propBarY, barWidth, barHeight, 12);
        this.ctx.stroke();

        // 内层装饰边框（圆角）
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)'; // 金色
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.roundRect(barX + 3, propBarY + 3, barWidth - 6, barHeight - 6, 8);
        this.ctx.stroke();

        // 道具配置（使用配置系统）
        let props;
        if (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.PROPS) {
            props = [
                {
                    type: 'refresh',
                    name: GAME_CONFIG.PROPS.NAMES.refresh,
                    shortName: GAME_CONFIG.PROPS.SHORT_NAMES.refresh,
                    count: this.core.props.refresh,
                    color: GAME_CONFIG.PROPS.COLORS.refresh,
                    icon: GAME_CONFIG.PROPS.ICONS.refresh
                },
                {
                    type: 'bomb',
                    name: GAME_CONFIG.PROPS.NAMES.bomb,
                    shortName: GAME_CONFIG.PROPS.SHORT_NAMES.bomb,
                    count: this.core.props.bomb,
                    color: GAME_CONFIG.PROPS.COLORS.bomb,
                    icon: GAME_CONFIG.PROPS.ICONS.bomb
                },
                {
                    type: 'clear',
                    name: GAME_CONFIG.PROPS.NAMES.clear,
                    shortName: GAME_CONFIG.PROPS.SHORT_NAMES.clear,
                    count: this.core.props.clear,
                    color: GAME_CONFIG.PROPS.COLORS.clear,
                    icon: GAME_CONFIG.PROPS.ICONS.clear
                }
            ];
        } else {
            // 回退到默认配置
            props = [
                { type: 'refresh', name: '刷新卡', shortName: '刷新', count: this.core.props.refresh, color: '#4CAF50', icon: '🔄' },
                { type: 'bomb', name: '炸弹卡', shortName: '炸弹', count: this.core.props.bomb, color: '#F44336', icon: '💣' },
                { type: 'clear', name: '清屏卡', shortName: '清屏', count: this.core.props.clear, color: '#2196F3', icon: '✨' }
            ];
        }

        // 获取道具栏配置
        const buttonMarginTop = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_MARGIN_TOP) ?
                               GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_MARGIN_TOP : 8;
        const textMarginTop = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.TEXT_MARGIN_TOP) ?
                             GAME_CONFIG.LAYOUT.PROPS_BAR.TEXT_MARGIN_TOP : 5;

        props.forEach((prop, index) => {
            // 计算道具按钮的X坐标
            let x;
            if (layoutMode === 'EQUAL_SPLIT') {
                // 平分布局：每个道具在其区域内居中
                x = startX + index * propSpacing - propSize / 2;
            } else {
                // 传统布局
                x = startX + index * propSpacing;
            }

            const y = propBarY + buttonMarginTop;  // 道具按钮向下移动

            // 道具按钮背景
            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 5;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;

            // 根据数量决定按钮状态
            const isAvailable = prop.count > 0;
            this.ctx.globalAlpha = isAvailable ? 1 : 0.5;

            // 获取圆角半径
            const buttonRadius = (typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_RADIUS) ?
                               GAME_CONFIG.LAYOUT.PROPS_BAR.BUTTON_RADIUS : 8;

            // 道具按钮背景（圆角）
            this.ctx.fillStyle = prop.color;
            this.ctx.beginPath();
            this.ctx.roundRect(x, y, propSize, propSize, buttonRadius);
            this.ctx.fill();

            // 道具按钮边框（圆角）
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            this.ctx.roundRect(x, y, propSize, propSize, buttonRadius);
            this.ctx.stroke();

            this.ctx.restore();

            // 道具图标（使用图片）
            const propImage = this.core.propImages[prop.type];
            if (propImage && propImage.complete) {
                const imageSize = propSize * 0.7;
                this.ctx.drawImage(
                    propImage,
                    x + (propSize - imageSize) / 2,
                    y + (propSize - imageSize) / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = 'bold 12px Arial, "Microsoft YaHei"';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText(prop.name, x + propSize/2, y + propSize/2);
            }

            // 道具数量（红色字体显示在右上角）
            if (prop.count > 0) {
                this.ctx.fillStyle = '#FF0000';
                this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 数量背景圆圈
                this.ctx.beginPath();
                this.ctx.arc(x + propSize - 10, y + 10, 12, 0, Math.PI * 2);
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.fill();
                this.ctx.strokeStyle = '#FF0000';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();

                // 数量文字
                this.ctx.fillStyle = '#FF0000';
                this.ctx.fillText(prop.count.toString(), x + propSize - 10, y + 10);
            }

            // 激活状态指示
            if (this.core.propUsing.isActive && this.core.propUsing.type === prop.type) {
                this.ctx.strokeStyle = '#FFD700';
                this.ctx.lineWidth = 4;
                this.ctx.strokeRect(x - 2, y - 2, propSize + 4, propSize + 4);

                // 添加闪烁效果
                this.ctx.save();
                this.ctx.globalAlpha = 0.5 + 0.5 * Math.sin(this.animationTime * 5);
                this.ctx.fillStyle = '#FFD700';
                this.ctx.fillRect(x, y, propSize, propSize);
                this.ctx.restore();
            }

            // 道具名称（在按钮下方）
            const textY = y + propSize + textMarginTop;
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 12px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'top';

            // 添加文字阴影效果
            this.ctx.save();
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.8)';
            this.ctx.shadowBlur = 2;
            this.ctx.shadowOffsetX = 1;
            this.ctx.shadowOffsetY = 1;
            this.ctx.fillText(prop.shortName, x + propSize/2, textY);
            this.ctx.restore();
        });

        // 道具使用提示
        if (this.core.propUsing.isActive) {
            this.ctx.fillStyle = '#FF69B4';
            this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';

            let tipText = '';
            if (this.core.propUsing.type === 'bomb') {
                tipText = '请点击要爆炸的位置';
            }

            if (tipText) {
                this.ctx.strokeStyle = '#FFFFFF';
                this.ctx.lineWidth = 2;
                this.ctx.strokeText(tipText, this.canvas.width / 2, propBarY + propSize + 30);
                this.ctx.fillText(tipText, this.canvas.width / 2, propBarY + propSize + 30);
            }
        }
    }
    
    // 渲染按钮
    renderButton(x, y, width, height, text, color) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 按钮背景渐变
        const buttonGradient = this.ctx.createLinearGradient(x, y, x, y + height);
        buttonGradient.addColorStop(0, color);
        buttonGradient.addColorStop(0.5, this.lightenColor(color, 20));
        buttonGradient.addColorStop(1, this.darkenColor(color, 10));
        
        this.ctx.fillStyle = buttonGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 按钮文字
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(text, x + width / 2, y + height / 2);
        
        this.ctx.restore();
    }
    
    // 渲染返回按钮（半透明白色背景，黑色字体）
    renderBackButton(x, y, width, height, text) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 半透明白色背景（透明度0.6）
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.lineWidth = 1;
        this.ctx.stroke();
        
        // 黑色文字（确保完全居中）
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 精确计算文本位置以确保完全居中
        const textX = Math.round(x + width / 2);
        const textY = Math.round(y + height / 2);
        this.ctx.fillText(text, textX, textY);
        
        this.ctx.restore();
    }
    

    
    // 渲染游戏网格
    renderGrid() {
        if (!this.core.grid) return;
        
        const startX = this.core.gridStartX;
        const startY = this.core.gridStartY;
        
        // 网格背景
        this.renderGridBackground(startX, startY);
        
        // 渲染网格中的方块（考虑间距）
        const spacing = typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.GRID ? GAME_CONFIG.GRID.SPACING : 2;
        for (let row = 0; row < this.core.gridSizeY; row++) {
            for (let col = 0; col < this.core.gridSizeX; col++) {
                const block = this.core.grid[row][col];
                if (block && !block.isFalling) {
                    const x = startX + col * (this.core.blockSize + spacing);
                    const y = startY + row * (this.core.blockSize + spacing);
                    this.renderBlock(block, x, y);
                }
            }
        }
        
        // 渲染掉落中的方块
        if (this.core.animator && this.core.animator.falling) {
            this.core.animator.render(this);
        }
    }
    
    // 渲染网格背景（使用配置系统）
    renderGridBackground(startX, startY) {
        // 使用配置系统计算网格背景尺寸
        let gridBackgroundWidth, gridBackgroundHeight, backgroundX, backgroundY;

        if (typeof CONFIG_UTILS !== 'undefined') {
            const layoutCalc = CONFIG_UTILS.calculateLayout(this.canvas.width, this.canvas.height);
            gridBackgroundWidth = layoutCalc.gridBackgroundWidth;
            gridBackgroundHeight = layoutCalc.gridBackgroundHeight;
            backgroundX = layoutCalc.gridArea.x;
            backgroundY = layoutCalc.gridArea.y;

            console.log(`网格背景: 位置(${backgroundX}, ${backgroundY}), 尺寸${gridBackgroundWidth}x${gridBackgroundHeight}`);
        } else {
            // 回退到原有计算
            const spacing = 2;
            const gridWidth = this.core.gridSizeX * this.core.blockSize + (this.core.gridSizeX - 1) * spacing;
            const gridHeight = this.core.gridSizeY * this.core.blockSize + (this.core.gridSizeY - 1) * spacing;
            gridBackgroundWidth = gridWidth + 20;
            gridBackgroundHeight = gridHeight + 20;
            backgroundX = startX - 10;
            backgroundY = startY - 10;
        }
        
        // 半透明白色背景
        this.ctx.fillStyle = this.colors.grid.background;
        this.ctx.shadowColor = this.colors.grid.shadow;
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 5;
        
        this.ctx.beginPath();
        this.ctx.roundRect(backgroundX, backgroundY, gridBackgroundWidth, gridBackgroundHeight, 15);
        this.ctx.fill();
        
        this.ctx.shadowColor = 'transparent';
        
        // 网格线（可选）
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.lineWidth = 1;
        
        // 获取间距配置
        const spacing = typeof GAME_CONFIG !== 'undefined' && GAME_CONFIG.GRID ? GAME_CONFIG.GRID.SPACING : 2;

        // 垂直线（在间距中央）
        for (let col = 1; col < this.core.gridSizeX; col++) {
            const x = startX + col * (this.core.blockSize + spacing) - spacing/2;
            this.ctx.beginPath();
            this.ctx.moveTo(x, startY);
            this.ctx.lineTo(x, startY + (this.core.gridSizeY * this.core.blockSize + (this.core.gridSizeY - 1) * spacing));
            this.ctx.stroke();
        }

        // 水平线（在间距中央）
        for (let row = 1; row < this.core.gridSizeY; row++) {
            const y = startY + row * (this.core.blockSize + spacing) - spacing/2;
            this.ctx.beginPath();
            this.ctx.moveTo(startX, y);
            this.ctx.lineTo(startX + (this.core.gridSizeX * this.core.blockSize + (this.core.gridSizeX - 1) * spacing), y);
            this.ctx.stroke();
        }
    }
    
    // 渲染方块（静态版本，无浮动动画）
    renderBlock(block, x, y) {
        this.ctx.save();
        
        // 应用透明度和缩放（仅选中状态有轻微缩放）
        this.ctx.globalAlpha = block.alpha;
        
        let scaleOffset = block.scale;
        if (block.isSelected) {
            scaleOffset = 1.1 + Math.sin(this.animationTime * 10) * 0.05; // 更明显的缩放和脉动
        }
        
        this.ctx.translate(x + this.core.blockSize / 2, y + this.core.blockSize / 2);
        this.ctx.scale(scaleOffset, scaleOffset);
        this.ctx.rotate(block.rotation);
        
        // 方块阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 方块背景渐变 - 支持特殊方块
        let blockColor;
        if (block.blockType === 'special') {
            blockColor = this.colors.special[block.type] || this.colors.special['rocket'];
        } else {
            blockColor = this.colors.animals[block.type] || this.colors.animals['cat'];
        }

        const blockGradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, this.core.blockSize / 2);
        blockGradient.addColorStop(0, blockColor.glow);
        blockGradient.addColorStop(0.7, blockColor.bg);
        blockGradient.addColorStop(1, this.darkenColor(blockColor.bg, 15));
        
        this.ctx.fillStyle = blockGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(-this.core.blockSize / 2, -this.core.blockSize / 2, 
                         this.core.blockSize, this.core.blockSize, 8);
        this.ctx.fill();
        
        // 方块边框
        this.ctx.shadowColor = 'transparent';
        if (block.isSelected) {
            // 选中状态：金色发光边框
            this.ctx.strokeStyle = '#FFD700';
            this.ctx.lineWidth = 4;
            this.ctx.shadowColor = '#FFD700';
            this.ctx.shadowBlur = 8;
            this.ctx.stroke();

            // 添加内层白色边框
            this.ctx.shadowColor = 'transparent';
            this.ctx.strokeStyle = '#FFFFFF';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
        } else {
            this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
        }
        
        // 内部高光
        const highlightGradient = this.ctx.createLinearGradient(
            -this.core.blockSize / 2, -this.core.blockSize / 2,
            this.core.blockSize / 4, this.core.blockSize / 4
        );
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        
        this.ctx.fillStyle = highlightGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(-this.core.blockSize / 2, -this.core.blockSize / 2, 
                         this.core.blockSize, this.core.blockSize, 8);
        this.ctx.fill();
        
        // 方块内容渲染
        this.ctx.shadowColor = 'transparent';

        if (block.blockType === 'special') {
            // 渲染特殊方块图标
            this.renderSpecialBlockIcon(block);
        } else {
            // 渲染普通萌宠
            const animalImage = this.core.animalImages[block.type];
            if (animalImage && animalImage.complete) {
                // 绘制萌宠图片
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    animalImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案 - 显示文字
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 显示萌宠名称的中文
                const animalNames = {
                    'cat': '猫',
                    'dog': '狗',
                    'elephant': '象',
                    'fox': '狐',
                    'frog': '蛙',
                    'monkey': '猴',
                    'panda': '熊',
                    'rabbit': '兔',
                    'tiger': '虎'
                };

                this.ctx.fillText(animalNames[block.type] || block.type, 0, 0);
            }
        }
        
        this.ctx.restore();
    }

    // 渲染特殊方块图标
    renderSpecialBlockIcon(block) {
        if (block.type === 'rocket') {
            // 尝试使用火箭图片
            const rocketImage = this.core.propImages['rocket'];
            if (rocketImage && rocketImage.complete) {
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    rocketImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('火箭', 0, 0);
            }

            // 添加闪烁效果
            this.ctx.save();
            this.ctx.globalAlpha = 0.5 + 0.5 * Math.sin(this.animationTime * 6);
            this.ctx.fillStyle = '#FFD700';
            this.ctx.beginPath();
            this.ctx.arc(0, 0, this.core.blockSize * 0.4, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();

        } else if (block.type === 'bomb') {
            // 尝试使用炸弹图片
            const bombImage = this.core.propImages['bomb_extra'];
            if (bombImage && bombImage.complete) {
                const imageSize = this.core.blockSize * 0.7;
                this.ctx.drawImage(
                    bombImage,
                    -imageSize / 2,
                    -imageSize / 2,
                    imageSize,
                    imageSize
                );
            } else {
                // 图片未加载时的备用方案
                this.ctx.fillStyle = '#FFFFFF';
                this.ctx.font = `bold ${this.core.blockSize * 0.3}px Arial, "Microsoft YaHei"`;
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText('炸弹', 0, 0);
            }

            // 添加危险闪烁效果
            this.ctx.save();
            this.ctx.globalAlpha = 0.3 + 0.3 * Math.sin(this.animationTime * 8);
            this.ctx.fillStyle = '#FF0000';
            this.ctx.beginPath();
            this.ctx.arc(0, 0, this.core.blockSize * 0.45, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        }
    }
    
    // 渲染特效
    renderEffects() {
        // 渲染粒子效果
        this.renderParticles();
        
        // 渲染闪烁效果
        this.renderSparkles();
        
        // 渲染浮动文字
        this.renderFloatingTexts();
    }
    
    // 渲染粒子效果
    renderParticles() {
        this.core.particles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }
    
    // 渲染闪烁效果
    renderSparkles() {
        this.core.sparkles.forEach(sparkle => {
            this.ctx.save();
            this.ctx.globalAlpha = sparkle.life * (0.5 + 0.5 * Math.sin(sparkle.twinkle));
            this.ctx.fillStyle = '#FFD700';
            this.ctx.translate(sparkle.x, sparkle.y);
            this.ctx.rotate(sparkle.twinkle);
            
            // 绘制星形
            this.ctx.beginPath();
            for (let i = 0; i < 4; i++) {
                const angle = (i * Math.PI) / 2;
                const x = Math.cos(angle) * sparkle.size;
                const y = Math.sin(angle) * sparkle.size;
                if (i === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            }
            this.ctx.closePath();
            this.ctx.fill();
            
            this.ctx.restore();
        });
    }
    
    // 渲染浮动文字
    renderFloatingTexts() {
        this.core.floatingTexts.forEach(text => {
            this.ctx.save();

            if (text.isCombo) {
                // 连击文字特殊效果
                this.renderComboText(text);
            } else {
                // 普通浮动文字 - 增大字体大小
                this.ctx.globalAlpha = text.life;
                this.ctx.fillStyle = text.color;
                this.ctx.font = `bold ${28 * text.scale}px Arial, "Microsoft YaHei"`; // 从20改为28
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';

                // 文字描边效果
                this.ctx.strokeStyle = '#FFFFFF';
                this.ctx.lineWidth = 3; // 增加描边宽度
                this.ctx.strokeText(text.text, text.x, text.y);
                this.ctx.fillText(text.text, text.x, text.y);
            }

            this.ctx.restore();
        });
    }

    // 渲染连击文字特效
    renderComboText(text) {
        // 脉动效果
        text.pulsePhase += 0.2;
        const pulseScale = 1 + Math.sin(text.pulsePhase) * 0.2;
        const finalScale = text.scale * pulseScale;

        // 透明度
        this.ctx.globalAlpha = text.life;

        // 阴影效果
        this.ctx.shadowColor = text.color;
        this.ctx.shadowBlur = 20;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 0;

        // 文字样式
        this.ctx.fillStyle = text.color;
        this.ctx.font = `bold ${30 * finalScale}px Arial, "Microsoft YaHei"`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // 多层描边效果
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 6;
        this.ctx.strokeText(text.text, text.x, text.y);

        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 3;
        this.ctx.strokeText(text.text, text.x, text.y);

        // 填充文字
        this.ctx.fillText(text.text, text.x, text.y);

        // 清除阴影
        this.ctx.shadowColor = 'transparent';
    }
    
    // 渲染游戏状态
    renderGameStatus() {
        if (this.core.showExitDialog) {
            this.renderExitDialog();
        } else if (this.core.isLevelComplete) {
            this.renderVictoryScreen();
        } else if (this.core.isGameOver) {
            this.renderGameOverScreen();
        }
    }
    
    // 渲染退出确认弹框
    renderExitDialog() {
        // 半透明遮罩
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // 弹框背景
        const dialogWidth = 300;
        const dialogHeight = 180;
        const dialogX = centerX - dialogWidth / 2;
        const dialogY = centerY - dialogHeight / 2;
        
        // 弹框阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 0;
        this.ctx.shadowOffsetY = 5;
        
        // 弹框背景渐变
        const dialogGradient = this.ctx.createLinearGradient(dialogX, dialogY, dialogX, dialogY + dialogHeight);
        dialogGradient.addColorStop(0, '#FFFFFF');
        dialogGradient.addColorStop(1, '#F8F9FA');
        
        this.ctx.fillStyle = dialogGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(dialogX, dialogY, dialogWidth, dialogHeight, 15);
        this.ctx.fill();
        
        // 弹框边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = '#FF69B4';
        this.ctx.lineWidth = 3;
        this.ctx.stroke();
        
        // 标题
        this.ctx.fillStyle = '#FF1493';
        this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('确认退出', centerX, centerY - 40);
        
        // 提示文字
        this.ctx.fillStyle = '#666666';
        this.ctx.font = '18px Arial, "Microsoft YaHei"';
        this.ctx.fillText('确定要退出当前游戏吗？', centerX, centerY - 10);
        this.ctx.fillText('游戏进度将会丢失', centerX, centerY + 10);
        
        // 继续游戏按钮
        this.renderButton(centerX - 120, centerY + 20, 100, 40, '继续游戏', '#4CAF50');
        
        // 返回主页按钮
        this.renderButton(centerX + 20, centerY + 20, 100, 40, '返回主页', '#FF6B6B');
    }
    
    // 渲染胜利界面
    renderVictoryScreen() {
        // 半透明遮罩
        this.ctx.fillStyle = 'rgba(255, 105, 180, 0.9)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // 胜利文字
        this.ctx.fillStyle = '#FFD700';
        this.ctx.font = 'bold 48px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 4;
        this.ctx.strokeText('恭喜过关！', centerX, centerY - 50);
        this.ctx.fillText('恭喜过关！', centerX, centerY - 50);
        
        // 分数统计
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '24px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`最终分数: ${this.core.score}`, centerX, centerY + 20);
        
        // 提示文字
        this.ctx.font = '18px Arial, "Microsoft YaHei"';
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.fillText('点击屏幕继续', centerX, centerY + 100);
    }
    
    // 渲染游戏结束界面
    renderGameOverScreen() {
        // 半透明遮罩
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        
        // 游戏结束文字
        this.ctx.fillStyle = '#FF6B6B';
        this.ctx.font = 'bold 42px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('游戏结束', centerX, centerY - 50);
        
        // 分数统计
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = '20px Arial, "Microsoft YaHei"';
        this.ctx.fillText(`最终分数: ${this.core.score}`, centerX, centerY);
        
        // 提示文字
        this.ctx.font = '16px Arial, "Microsoft YaHei"';
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.fillText('点击屏幕重新开始', centerX, centerY + 50);
    }

    // 渲染退出确认对话框
    renderExitDialog() {
        // 半透明遮罩
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const dialogWidth = 300;
        const dialogHeight = 200;

        // 对话框背景
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
        this.ctx.fillRect(centerX - dialogWidth/2, centerY - dialogHeight/2, dialogWidth, dialogHeight);

        // 对话框边框
        this.ctx.strokeStyle = '#FF69B4';
        this.ctx.lineWidth = 3;
        this.ctx.strokeRect(centerX - dialogWidth/2, centerY - dialogHeight/2, dialogWidth, dialogHeight);

        // 标题
        this.ctx.fillStyle = '#FF1493';
        this.ctx.font = 'bold 24px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('确认退出？', centerX, centerY - 40);

        // 按钮
        const buttonWidth = 100;
        const buttonHeight = 40;
        const buttonY = centerY + 30;

        // 继续游戏按钮
        this.ctx.fillStyle = '#4CAF50';
        this.ctx.fillRect(centerX - buttonWidth - 10, buttonY - buttonHeight/2, buttonWidth, buttonHeight);
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(centerX - buttonWidth - 10, buttonY - buttonHeight/2, buttonWidth, buttonHeight);

        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.fillText('继续游戏', centerX - buttonWidth/2 - 10, buttonY);

        // 退出按钮
        this.ctx.fillStyle = '#F44336';
        this.ctx.fillRect(centerX + 10, buttonY - buttonHeight/2, buttonWidth, buttonHeight);
        this.ctx.strokeRect(centerX + 10, buttonY - buttonHeight/2, buttonWidth, buttonHeight);

        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.fillText('退出', centerX + buttonWidth/2 + 10, buttonY);

        // 存储按钮位置供点击检测使用
        this.exitDialogButtons = {
            continue: {
                x: centerX - buttonWidth - 10,
                y: buttonY - buttonHeight/2,
                width: buttonWidth,
                height: buttonHeight
            },
            exit: {
                x: centerX + 10,
                y: buttonY - buttonHeight/2,
                width: buttonWidth,
                height: buttonHeight
            }
        };
    }

    // 渲染炸弹卡拖拽
    renderBombDrag() {
        const events = this.core.events;
        if (!events || !events.isDraggingBomb) return;

        const x = events.bombDragCurrentX;
        const y = events.bombDragCurrentY;
        const size = 50;

        // 半透明炸弹图标
        this.ctx.save();
        this.ctx.globalAlpha = 0.8;

        // 炸弹背景
        this.ctx.fillStyle = '#F44336';
        this.ctx.fillRect(x - size/2, y - size/2, size, size);

        // 炸弹图片
        const bombImage = this.core.propImages['bomb'];
        if (bombImage && bombImage.complete) {
            const imageSize = size * 0.8;
            this.ctx.drawImage(
                bombImage,
                x - imageSize/2,
                y - imageSize/2,
                imageSize,
                imageSize
            );
        } else {
            // 备用文字
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 12px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText('炸弹', x, y);
        }

        this.ctx.restore();

        // 绘制拖拽轨迹
        this.ctx.save();
        this.ctx.strokeStyle = 'rgba(244, 67, 54, 0.5)';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([5, 5]);
        this.ctx.beginPath();
        this.ctx.moveTo(events.bombDragStartX, events.bombDragStartY);
        this.ctx.lineTo(x, y);
        this.ctx.stroke();
        this.ctx.restore();
    }
    
    // 获取按钮信息（供事件处理使用）
    getButtons() {
        // 使用新的布局配置
        let backButtonConfig;
        if (typeof CONFIG_UTILS !== 'undefined') {
            const layoutCalc = CONFIG_UTILS.calculateLayout(this.canvas.width, this.canvas.height);
            backButtonConfig = layoutCalc.backButton;
        } else {
            backButtonConfig = { x: 20, y: 30, width: 100, height: 32 };
        }

        const buttons = {
            back: {
                id: 'back',
                x: backButtonConfig.x,
                y: backButtonConfig.y,
                width: backButtonConfig.width,
                height: backButtonConfig.height,
                text: '返回'
            }
        };
        
        // 如果显示退出确认弹框，添加弹框按钮
        if (this.core.showExitDialog) {
            buttons.continueGame = {
                id: 'continueGame',
                x: this.canvas.width / 2 - 120,
                y: this.canvas.height / 2 + 20,
                width: 100,
                height: 40,
                text: '继续游戏'
            };
            buttons.exitGame = {
                id: 'exitGame',
                x: this.canvas.width / 2 + 20,
                y: this.canvas.height / 2 + 20,
                width: 100,
                height: 40,
                text: '返回主页'
            };
        }
        
        return buttons;
    }
    
    // 检查点击是否在按钮内
    isPointInButton(x, y, button) {
        return x >= button.x && 
               x <= button.x + button.width && 
               y >= button.y && 
               y <= button.y + button.height;
    }
    
    // 颜色工具函数
    lightenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
            (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
            (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageRenderer;
} else {
    window.GamePageRenderer = GamePageRenderer;
}