/**
 * 美化版排行榜页面 - 模块化整合
 * 整合核心、渲染和事件处理模块
 */
class BeautifulRankPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        
        // 初始化各个模块
        this.core = new RankPageCore(gameManager);
        this.renderer = new RankPageRenderer(this.core);
        this.events = new RankPageEvents(this.core, this.renderer);
        
        console.log('BeautifulRankPage初始化完成');
    }
    
    // 初始化页面
    init() {
        console.log('初始化美化版排行榜页面');
        
        // 初始化事件处理
        this.events.init();
        
        console.log('美化版排行榜页面初始化完成');
    }
    
    // 更新页面状态
    update() {
        // 更新渲染器动画
        this.renderer.update();
    }
    
    // 渲染页面
    render() {
        // 清空画布
        this.renderer.ctx.clearRect(0, 0, this.renderer.canvas.width, this.renderer.canvas.height);
        
        // 渲染主要内容
        this.renderer.render();
        
        // 渲染按钮
        this.events.renderButtons();
    }
    
    // 切换标签页
    switchTab(tab) {
        this.events.switchTab(tab);
    }
    
    // 获取当前排行榜数据
    getCurrentRankData() {
        return this.core.getCurrentRankData();
    }
    
    // 获取当前标签页名称
    getCurrentTabName() {
        return this.core.getCurrentTabName();
    }
    
    // 刷新排行榜数据
    async refreshRankData() {
        try {
            console.log('刷新排行榜数据...');
            const currentTab = this.core.currentTab;
            const newData = await this.core.fetchRankData(currentTab);
            this.core.updateRankData(currentTab, newData);
            console.log('排行榜数据刷新完成');
        } catch (error) {
            console.error('刷新排行榜数据失败:', error);
        }
    }
    
    // 添加玩家到排行榜
    addPlayerToRank(playerData) {
        const currentTab = this.core.currentTab;
        this.core.addPlayerToRank(currentTab, playerData);
    }
    
    // 获取玩家排名
    getPlayerRanking() {
        return this.core.getPlayerRanking();
    }
    
    // 处理窗口大小变化
    handleResize() {
        this.events.updateButtonLayout();
        this.renderer.initBackground();
    }
    
    // 销毁页面
    destroy() {
        console.log('销毁美化版排行榜页面');
        
        if (this.events) {
            this.events.destroy();
        }
        
        console.log('美化版排行榜页面销毁完成');
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BeautifulRankPage;
} else {
    window.BeautifulRankPage = BeautifulRankPage;
}