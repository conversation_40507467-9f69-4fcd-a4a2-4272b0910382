/**
 * 排行榜页面渲染模块 - 负责所有视觉效果绘制
 */
class RankPageRenderer {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        this.animationTime = 0;
        
        // 背景效果
        this.backgroundStars = null;
        this.initBackground();
        
        console.log('RankPageRenderer初始化完成');
    }
    
    // 初始化背景效果
    initBackground() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundStars = BackgroundUtils.createStars(15, this.canvas.width, this.canvas.height);
            }
        } catch (error) {
            console.warn('背景效果初始化失败，使用简化背景');
        }
    }
    
    // 更新动画
    update() {
        this.animationTime += 0.02;
        
        // 更新背景星星
        if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
            BackgroundUtils.updateStars(this.backgroundStars);
        }
    }
    
    // 主渲染方法
    render() {
        // 绘制背景
        this.drawBackground();
        
        const centerX = this.canvas.width / 2;
        
        // 绘制标题
        this.drawTitle(centerX);
        
        // 绘制玩家成绩卡片
        this.drawPlayerCard();
        
        // 绘制排行榜列表
        this.drawRankList();
    }
    
    // 绘制背景
    drawBackground() {
        if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
            BackgroundUtils.drawCompleteBackground(
                this.ctx,
                this.canvas.width,
                this.canvas.height,
                this.backgroundStars,
                this.animationTime
            );
        } else {
            // 简化背景
            const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
    }
    
    // 绘制标题
    drawTitle(centerX) {
        this.ctx.save();
        
        // 添加标题阴影效果
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 使用渐变色标题
        const gradient = this.ctx.createLinearGradient(centerX - 100, 70, centerX + 100, 100);
        gradient.addColorStop(0, '#FFD700');  // 金色
        gradient.addColorStop(1, '#FF8C00');  // 橙色
        
        this.ctx.fillStyle = gradient;
        this.ctx.font = 'bold 36px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('排行榜', centerX, 100);
        
        // 添加装饰性下划线
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - 80, 110);
        this.ctx.lineTo(centerX + 80, 110);
        this.ctx.lineWidth = 3;
        this.ctx.strokeStyle = '#FFD700';
        this.ctx.stroke();
        
        this.ctx.restore();
    }
    
    // 绘制玩家成绩卡片
    drawPlayerCard() {
        const cardY = 220;
        const cardHeight = 90;
        
        this.ctx.save();
        
        // 添加卡片阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 使用渐变背景
        const gradient = this.ctx.createLinearGradient(0, cardY, 0, cardY + cardHeight);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0.7)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(50, cardY, this.canvas.width - 100, cardHeight, 15);
        this.ctx.fill();
        
        // 添加卡片边框
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.restore();
        
        // 添加玩家头像/图标
        // 绘制玩家信息（去掉头像）
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'left';
        this.ctx.fillText('我的最佳成绩', 70, cardY + 30);
        
        // 使用醒目的分数显示
        this.ctx.fillStyle = '#FF6B35';
        this.ctx.font = 'bold 28px Arial';
        this.ctx.fillText(`${this.core.gameManager.gameData.playerData.bestScore} 分`, 70, cardY + 65);
        
        // 绘制排名信息
        // 绘制排名信息
        // 绘制排名信息
        const ranking = this.core.getPlayerRanking();
        this.ctx.fillStyle = '#666666';
        this.ctx.font = 'bold 16px Arial';
        this.ctx.textAlign = 'right';
        this.ctx.fillText('当前排名:', this.canvas.width - 130, cardY + 45); // 从-120改为-130，向左移动10像素
        
        // 使用深色数字，确保清晰可见
        this.ctx.fillStyle = '#333333'; // 改为深灰色，更清晰
        this.ctx.font = 'bold 24px Arial';
        this.ctx.fillText(`#${ranking}`, this.canvas.width - 80, cardY + 45); // 从-70改为-80，向左移动10像素
    }
    
    // 绘制排行榜列表
    // 绘制排行榜列表
    drawRankList() {
        const rankData = this.core.getCurrentRankData();
        const startY = 360;
        const itemHeight = 50;
        const headerHeight = 40;
        const padding = 20;
        
        // 动态计算实际需要的数据条数
        const actualDataCount = rankData.length;
        const maxDisplayCount = Math.min(actualDataCount, 10); // 最多显示10条
        
        // 动态计算列表高度
        const listContentHeight = maxDisplayCount * itemHeight;
        const totalListHeight = headerHeight + listContentHeight + padding;
        
        // 如果没有数据，显示空状态
        if (actualDataCount === 0) {
            this.drawEmptyState(startY);
            return;
        }
        
        this.ctx.save();
        
        // 添加列表阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 10;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 绘制动态高度的列表背景框
        const gradient = this.ctx.createLinearGradient(0, startY - 30, 0, startY - 30 + totalListHeight);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0.85)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(40, startY - 30, this.canvas.width - 80, totalListHeight, 15);
        this.ctx.fill();
        
        // 添加列表边框
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.restore();
        
        // 绘制表头
        this.drawListHeader(startY);
        
        // 显示实际数据条数（最多10条）
        const displayData = rankData.slice(0, maxDisplayCount);
        displayData.forEach((player, index) => {
            this.drawRankItem(player, index, startY + 30 + index * itemHeight);
        });
        
        // 如果数据少于10条，在底部显示提示信息
        if (actualDataCount < 10) {
            this.drawDataCountInfo(startY + 30 + listContentHeight + 10, actualDataCount);
        }
    }
    
    // 绘制空状态
    drawEmptyState(startY) {
        const emptyHeight = 120;
        
        this.ctx.save();
        
        // 绘制空状态背景
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.beginPath();
        this.ctx.roundRect(40, startY - 30, this.canvas.width - 80, emptyHeight, 15);
        this.ctx.fill();
        
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.4)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 绘制空状态文字
        this.ctx.shadowColor = 'transparent';
        this.ctx.fillStyle = '#999999';
        this.ctx.font = '18px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('暂无排行榜数据', this.canvas.width / 2, startY + 20);
        
        this.ctx.fillStyle = '#CCCCCC';
        this.ctx.font = '14px Arial, "Microsoft YaHei"';
        this.ctx.fillText('快去游戏中创造更好的成绩吧！', this.canvas.width / 2, startY + 50);
        
        this.ctx.restore();
    }
    
    // 绘制数据条数信息
    drawDataCountInfo(y, count) {
        this.ctx.save();
        
        this.ctx.fillStyle = '#999999';
        this.ctx.font = '12px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`共 ${count} 条记录`, this.canvas.width / 2, y);
        
        this.ctx.restore();
    }
    
    // 绘制表头
    drawListHeader(startY) {
        this.ctx.fillStyle = '#FF8C00';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        
        // 排名列
        this.ctx.textAlign = 'center';
        this.ctx.fillText('排名', 80, startY);
        
        // 昵称列
        this.ctx.textAlign = 'left';
        this.ctx.fillText('昵称', 120, startY);
        
        // 姓名列
        this.ctx.textAlign = 'center';
        this.ctx.fillText('姓名', this.canvas.width / 2, startY);
        
        // 分数列
        this.ctx.textAlign = 'right';
        this.ctx.fillText('分数', this.canvas.width - 70, startY);
        
        // 绘制表头分割线
        const gradient = this.ctx.createLinearGradient(50, startY + 10, this.canvas.width - 50, startY + 10);
        gradient.addColorStop(0, 'rgba(255, 140, 0, 0.2)');
        gradient.addColorStop(0.5, 'rgba(255, 140, 0, 0.8)');
        gradient.addColorStop(1, 'rgba(255, 140, 0, 0.2)');
        
        this.ctx.strokeStyle = gradient;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(50, startY + 10);
        this.ctx.lineTo(this.canvas.width - 50, startY + 10);
        this.ctx.stroke();
    }
    
    // 绘制排行榜项目
    drawRankItem(player, index, y) {
        const isTopThree = this.core.isTopThree(player.ranking);
        const isEvenRow = index % 2 === 0;
        
        // 为偶数行添加轻微背景色
        if (isEvenRow) {
            this.ctx.fillStyle = 'rgba(255, 215, 0, 0.1)';
            this.ctx.fillRect(50, y - 20, this.canvas.width - 100, 45);
        }
        
        // 绘制排名
        if (isTopThree) {
            // 为前三名绘制更小的奖牌背景
            const medalColor = this.core.getRankingColor(player.ranking);
            this.ctx.fillStyle = medalColor;
            this.ctx.beginPath();
            this.ctx.arc(80, y - 5, 12, 0, Math.PI * 2); // 从18改为12，更小的圆圈
            this.ctx.fill();
            
            // 添加内圈边框效果
            this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.lineWidth = 1;
            this.ctx.stroke();
            
            // 绘制排名数字
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.font = 'bold 14px Arial'; // 从16px改为14px，更小的字体
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(`${player.ranking}`, 80, y - 5);
            this.ctx.textBaseline = 'alphabetic';
        } else {
            // 其他排名使用普通样式
            this.ctx.fillStyle = '#666666';
            this.ctx.font = 'bold 16px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`${player.ranking}`, 80, y);
        }
        
        // 绘制昵称
        this.ctx.fillStyle = isTopThree ? '#FF6B35' : '#333333';
        this.ctx.font = isTopThree ? 'bold 16px Arial, "Microsoft YaHei"' : '14px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(player.nickname, 120, y);
        
        // 绘制姓名
        this.ctx.fillStyle = '#666666';
        this.ctx.font = '12px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`(${player.name})`, this.canvas.width / 2, y);
        
        // 绘制分数
        const scoreColor = this.core.getRankingColor(player.ranking);
        this.ctx.fillStyle = isTopThree ? scoreColor : '#333333';
        this.ctx.font = isTopThree ? 'bold 18px Arial' : '16px Arial';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(`${player.score}分`, this.canvas.width - 70, y);
        
        // 为前三名添加文字标记而不是emoji
        // 前三名不再显示额外的文字标记
    }
    
    // 渲染返回按钮
    renderBackButton(x, y, width, height, text) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 半透明白色背景
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(255, 140, 0, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 文字
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        const textX = Math.round(x + width / 2);
        const textY = Math.round(y + height / 2);
        this.ctx.fillText(text, textX, textY);
        
        this.ctx.restore();
    }
    
    // 渲染标签按钮
    renderTabButton(button, isActive) {
        this.ctx.save();
        
        if (isActive) {
            // 激活状态的渐变背景
            const gradient = this.ctx.createLinearGradient(
                button.x, button.y, 
                button.x, button.y + button.height
            );
            gradient.addColorStop(0, '#FFD700');
            gradient.addColorStop(1, '#FF8C00');
            
            this.ctx.fillStyle = gradient;
            this.ctx.beginPath();
            this.ctx.roundRect(button.x, button.y, button.width, button.height, 20);
            this.ctx.fill();
            
            // 添加光泽效果
            const glossGradient = this.ctx.createLinearGradient(
                button.x, button.y, 
                button.x, button.y + button.height / 2
            );
            glossGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
            glossGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
            this.ctx.fillStyle = glossGradient;
            this.ctx.beginPath();
            this.ctx.roundRect(button.x, button.y, button.width, button.height / 2, 20);
            this.ctx.fill();
        }
        
        // 绘制按钮文字
        this.ctx.fillStyle = isActive ? '#FFFFFF' : 'rgba(255, 255, 255, 0.7)';
        this.ctx.font = isActive ? 'bold 18px Arial, "Microsoft YaHei"' : '16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 添加文字阴影效果（仅激活按钮）
        if (isActive) {
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            this.ctx.shadowBlur = 3;
            this.ctx.shadowOffsetX = 1;
            this.ctx.shadowOffsetY = 1;
        }
        
        this.ctx.fillText(button.text, button.x + button.width / 2, button.y + button.height / 2);
        
        // 为激活按钮添加底部指示条
        if (isActive) {
            this.ctx.shadowColor = 'transparent';
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.fillRect(button.x + 15, button.y + button.height - 3, button.width - 30, 3);
        }
        
        this.ctx.restore();
    }
    
    // 渲染标签按钮容器
    renderTabContainer(centerX, tabY, tabWidth, tabHeight) {
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.beginPath();
        this.ctx.roundRect(centerX - tabWidth/2, tabY, tabWidth, tabHeight, 20);
        this.ctx.fill();
        this.ctx.restore();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RankPageRenderer;
} else {
    window.RankPageRenderer = RankPageRenderer;
}