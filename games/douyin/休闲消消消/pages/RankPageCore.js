/**
 * 排行榜页面核心模块 - 数据管理和基础功能
 */
class RankPageCore {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        
        // 当前选中的标签页
        this.currentTab = 'global';
        
        // 排行榜数据 - 三种类型：全部、地区、好友
        this.rankData = {
            global: [
                { ranking: 1, nickname: '消除大师', name: '张三', score: 2500 },
                { ranking: 2, nickname: '萌宠达人', name: '李四', score: 2200 },
                { ranking: 3, nickname: '游戏高手', name: '王五', score: 2000 },
                { ranking: 4, nickname: '休闲玩家', name: '赵六', score: 1800 },
                { ranking: 5, nickname: '新手玩家', name: '钱七', score: 1600 },
                { ranking: 6, nickname: '积极玩家', name: '孙八', score: 1400 },
                { ranking: 7, nickname: '努力玩家', name: '周九', score: 1200 },
                { ranking: 8, nickname: '坚持玩家', name: '吴十', score: 1000 },
                { ranking: 9, nickname: '勤奋玩家', name: '郑一', score: 800 },
                { ranking: 10, nickname: '学习玩家', name: '王二', score: 600 }
            ],
            region: [
                { ranking: 1, nickname: '本地高手', name: '本地张三', score: 1950 },
                { ranking: 2, nickname: '同城达人', name: '同城李四', score: 1750 },
                { ranking: 3, nickname: '邻居玩家', name: '邻居王五', score: 1550 },
                { ranking: 4, nickname: '附近高手', name: '附近赵六', score: 1350 },
                { ranking: 5, nickname: '区域冠军', name: '区域钱七', score: 1150 },
                { ranking: 6, nickname: '地区精英', name: '地区孙八', score: 950 },
                { ranking: 7, nickname: '本地新星', name: '本地周九', score: 750 },
                { ranking: 8, nickname: '同城好手', name: '同城吴十', score: 550 },
                { ranking: 9, nickname: '邻里高手', name: '邻里郑一', score: 350 },
                { ranking: 10, nickname: '附近玩家', name: '附近王二', score: 150 }
            ],
            friend: [
                { ranking: 1, nickname: '好友小明', name: '明明', score: 1900 },
                { ranking: 2, nickname: '好友小红', name: '红红', score: 1700 },
                { ranking: 3, nickname: '好友小李', name: '李李', score: 1500 },
                { ranking: 4, nickname: '好友小王', name: '王王', score: 1300 },
                { ranking: 5, nickname: '好友小张', name: '张张', score: 1100 },
                { ranking: 6, nickname: '好友小刘', name: '刘刘', score: 900 },
                { ranking: 7, nickname: '好友小陈', name: '陈陈', score: 700 },
                { ranking: 8, nickname: '好友小林', name: '林林', score: 500 },
                { ranking: 9, nickname: '好友小周', name: '周周', score: 300 },
                { ranking: 10, nickname: '好友小吴', name: '吴吴', score: 100 }
            ]
        };
        
        console.log('RankPageCore初始化完成');
    }
    
    // 切换标签页
    switchTab(tab) {
        const tabNames = {
            'global': '全部',
            'region': '地区', 
            'friend': '好友'
        };
        console.log(`切换到${tabNames[tab]}排行榜`);
        this.currentTab = tab;
    }
    
    // 获取当前标签页的排行榜数据
    getCurrentRankData() {
        const data = this.rankData[this.currentTab] || [];
        // 只返回前10名
        return data.slice(0, 10);
    }
    
    // 获取玩家在当前排行榜中的排名
    getPlayerRanking() {
        const currentData = this.getCurrentRankData();
        const playerScore = this.gameManager.gameData.playerData.bestScore;
        
        // 找到玩家在当前排行榜中的位置
        let ranking = currentData.findIndex(player => player.score <= playerScore);
        if (ranking === -1) {
            ranking = currentData.length; // 如果分数比所有人都低，排在最后
        }
        
        return ranking + 1; // 排名从1开始
    }
    
    // 获取当前标签页名称
    getCurrentTabName() {
        const tabNames = {
            'global': '全部排行',
            'region': '地区排行', 
            'friend': '好友排行'
        };
        return tabNames[this.currentTab] || '排行榜';
    }
    
    // 检查是否为前三名
    isTopThree(ranking) {
        return ranking <= 3;
    }
    
    // 获取排名颜色
    getRankingColor(ranking) {
        if (ranking <= 3) {
            const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // 金、银、铜
            return colors[ranking - 1];
        }
        return '#FFFFFF';
    }
    
    // 获取排名图标
    // 获取排名标签
    getRankingLabel(ranking) {
        if (ranking <= 3) {
            const labels = ['冠军', '亚军', '季军'];
            return labels[ranking - 1];
        }
        return '';
    }
    
    // 模拟从服务器获取排行榜数据
    async fetchRankData(type) {
        console.log(`获取${type}排行榜数据`);
        // 这里可以添加实际的网络请求逻辑
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(this.rankData[type] || []);
            }, 100);
        });
    }
    
    // 更新排行榜数据
    updateRankData(type, data) {
        if (this.rankData[type]) {
            this.rankData[type] = data.slice(0, 10); // 只保留前10名
            console.log(`${type}排行榜数据已更新`);
        }
    }
    
    // 添加玩家到排行榜
    addPlayerToRank(type, playerData) {
        if (!this.rankData[type]) return;
        
        const rankList = this.rankData[type];
        rankList.push(playerData);
        
        // 按分数排序
        rankList.sort((a, b) => b.score - a.score);
        
        // 更新排名
        rankList.forEach((player, index) => {
            player.ranking = index + 1;
        });
        
        // 只保留前10名
        this.rankData[type] = rankList.slice(0, 10);
        
        console.log(`玩家已添加到${type}排行榜`);
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RankPageCore;
} else {
    window.RankPageCore = RankPageCore;
}