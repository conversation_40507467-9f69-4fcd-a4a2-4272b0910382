/**
 * 游戏页面掉落系统 - 处理方块掉落和重力效果
 */
class GamePageFalling {
    constructor(core) {
        this.core = core;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        
        // 掉落动画配置
        this.fallSpeed = 2; // 掉落速度 (放慢)
        this.fallAcceleration = 0.1; // 重力加速度 (减小)
        this.bounceEffect = 0.1; // 落地反弹效果
        
        // 掉落状态
        this.fallingBlocks = [];
        this.isProcessingFall = false;
        
        // 移除了连击系统
        
        console.log('GamePageFalling初始化完成');
    }
    
    // 处理方块消除后的掉落
    processFalling(removedPositions) {
        console.log('开始处理方块掉落', removedPositions);
        
        this.isProcessingFall = true;
        
        // 按列处理掉落
        const columnsToDrop = new Set();
        removedPositions.forEach(pos => {
            columnsToDrop.add(pos.col);
        });
        
        // 为每一列创建掉落动画
        columnsToDrop.forEach(col => {
            this.processColumnFalling(col, removedPositions);
        });
        
        // 生成新的方块从顶部掉落
        this.generateNewBlocks(removedPositions);
        
        return new Promise((resolve) => {
            // 等待所有掉落动画完成
            this.waitForFallingComplete(resolve);
        });
    }
    
    // 处理单列的掉落
    processColumnFalling(col, removedPositions) {
        // 获取该列被移除的位置
        const removedInColumn = removedPositions
            .filter(pos => pos.col === col)
            .map(pos => pos.row)
            .sort((a, b) => a - b); // 从上往下排序
        
        // 检查整列的空位情况
        const emptyPositions = [];
        for (let row = 0; row < this.core.gridSizeY; row++) {
            if (!this.core.grid[row][col] || this.core.grid[row][col].isRemoved) {
                emptyPositions.push(row);
            }
        }
        
        console.log(`列${col}的空位:`, emptyPositions);
        
        // 从下往上处理每个空位
        if (emptyPositions.length > 0) {
            // 先处理现有方块的掉落
            this.dropBlocksInColumn(col, emptyPositions);
            
            // 然后生成新方块填补剩余空位
            const remainingEmptyCount = this.countRemainingEmptyPositions(col);
            if (remainingEmptyCount > 0) {
                this.generateNewBlocksForColumn(col, remainingEmptyCount);
            }
        }
    }
    
    // 在指定列中掉落方块
    dropBlocksInColumn(col, emptyPositions) {
        if (!Array.isArray(emptyPositions)) {
            // 兼容旧的调用方式
            emptyPositions = [emptyPositions];
        }
        
        // 从下往上排序空位
        emptyPositions.sort((a, b) => b - a);
        
        // 找到所有需要掉落的方块
        const blocksToDrop = [];
        for (let row = 0; row < this.core.gridSizeY; row++) {
            const block = this.core.grid[row][col];
            if (block && !block.isFalling && !block.isRemoved) {
                blocksToDrop.push({ block, row });
            }
        }
        
        // 从下往上处理每个方块
        blocksToDrop.sort((a, b) => b.row - a.row);
        
        // 为每个方块计算目标位置
        let nextAvailableRow = this.core.gridSizeY - 1;
        
        blocksToDrop.forEach(({ block, row }) => {
            // 找到下一个可用的位置
            while (nextAvailableRow >= 0) {
                // 检查该位置是否为空
                const isEmpty = !this.core.grid[nextAvailableRow][col] || 
                               this.core.grid[nextAvailableRow][col].isRemoved;
                
                // 检查该位置是否已经有方块在掉落到这里
                const hasBlockFallingToThisPosition = this.fallingBlocks.some(
                    fb => fb.col === col && fb.toRow === nextAvailableRow
                );
                
                if (isEmpty && !hasBlockFallingToThisPosition) {
                    break;
                }
                
                nextAvailableRow--;
            }
            
            // 如果找到了可用位置，且位置在当前方块下方
            if (nextAvailableRow > row) {
                const targetRow = nextAvailableRow;
                this.startBlockFalling(block, row, col, targetRow);
                nextAvailableRow--; // 更新下一个可用位置
            }
        });
    }
    
    // 找到方块的目标行位置
    findTargetRow(col, startRow) {
        let targetRow = startRow;
        
        // 向下查找第一个非空且未移除的方块，或者到达网格底部
        for (let row = startRow + 1; row < this.core.gridSizeY; row++) {
            const block = this.core.grid[row][col];
            
            // 检查该位置是否已经有方块在掉落到这里
            const hasBlockFallingToThisPosition = this.fallingBlocks.some(
                fb => fb.col === col && fb.toRow === row
            );
            
            if ((!block || block.isRemoved) && !hasBlockFallingToThisPosition) {
                // 如果是空位或已移除的方块，且没有其他方块正在掉落到这个位置，更新目标行
                targetRow = row;
            } else {
                // 找到非空且未移除的方块，或者有方块正在掉落到这个位置，停止查找
                break;
            }
        }
        
        return targetRow;
    }
    
    // 开始方块掉落动画
    startBlockFalling(block, fromRow, col, toRow) {
        // 获取间距
        const spacing = this.core.getSpacing();

        // 标记为掉落状态
        block.isFalling = true;
        block.fallVelocity = this.fallSpeed; // 设置初始速度
        block.targetRow = toRow;
        block.startY = block.y;
        block.targetY = this.core.gridStartY + toRow * (this.core.blockSize + spacing);
        
        // 添加到掉落列表
        this.fallingBlocks.push({
            block: block,
            fromRow: fromRow,
            toRow: toRow,
            col: col,
            startTime: Date.now()
        });
        
        // 清空原位置
        this.core.grid[fromRow][col] = null;
        
        console.log(`方块开始掉落: (${fromRow}, ${col}) -> (${toRow}, ${col})`);
    }
    
    // 生成新的方块从顶部掉落
    generateNewBlocks(removedPositions) {
        // 统计每列需要生成的方块数量
        const newBlocksPerColumn = {};
        
        removedPositions.forEach(pos => {
            if (!newBlocksPerColumn[pos.col]) {
                newBlocksPerColumn[pos.col] = 0;
            }
            newBlocksPerColumn[pos.col]++;
        });
        
        // 为每列生成新方块
        Object.keys(newBlocksPerColumn).forEach(col => {
            const count = newBlocksPerColumn[col];
            this.generateNewBlocksForColumn(parseInt(col), count);
        });
    }
    
    // 为指定列生成新方块
    generateNewBlocksForColumn(col, count) {
        if (count <= 0) return;
        
        console.log(`为列${col}生成${count}个新方块`);
        
        // 找到所有真正的空位
        const emptyPositions = [];
        for (let row = 0; row < this.core.gridSizeY; row++) {
            // 检查网格中是否有有效方块（未被移除的方块）
            const hasValidBlock = this.core.grid[row][col] && !this.core.grid[row][col].isRemoved;

            // 检查是否有方块正在掉落到这个位置
            const hasBlockFallingToThisPosition = this.fallingBlocks.some(
                fb => fb.col === col && fb.toRow === row
            );

            // 只有完全空的位置才能生成新方块
            if (!hasValidBlock && !hasBlockFallingToThisPosition) {
                emptyPositions.push(row);
                console.log(`列${col}行${row}为空位，可生成新方块`);
            } else if (hasValidBlock) {
                console.log(`列${col}行${row}已有方块: ${this.core.grid[row][col].animalType}`);
            }
        }
        
        // 准备填充位置（从底部开始）
        const fillPositions = emptyPositions.slice(-count).map(row => ({ row, col }));

        console.log(`火箭消除后补充: 列${col}需要${count}个, 选择位置:`, fillPositions.map(p => `(${p.row},${p.col})`));

        // 使用公用填充方法
        const newBlocks = this.core.fillBlocks(fillPositions, {
            fillOrder: 'BOTTOM_UP',     // 从下往上填充
            startRow: 0,                // 从第一排开始
            fallSpeed: this.fallSpeed   // 使用当前下落速度
        });

        // 将新格子添加到下落列表（保持原有的下落管理逻辑）
        newBlocks.forEach((newBlock, index) => {
            this.fallingBlocks.push({
                block: newBlock,
                fromRow: 0,
                toRow: newBlock.targetRow,
                col: col,
                startTime: Date.now() + index * 100, // 错开下落时间
                isNew: true
            });

            console.log(`生成新方块掉落: 列${col}, 目标行${newBlock.targetRow}`);
        });

        console.log(`火箭消除补充完成: 列${col}创建了${newBlocks.length}个新格子`);
    }
    
    // 计算列中剩余的空位数量
    countRemainingEmptyPositions(col) {
        let count = 0;
        for (let row = 0; row < this.core.gridSizeY; row++) {
            // 检查网格中是否有方块
            const hasBlock = this.core.grid[row][col] && !this.core.grid[row][col].isRemoved;
            
            // 检查是否有方块正在掉落到这个位置
            const hasBlockFallingToThisPosition = this.fallingBlocks.some(
                fb => fb.col === col && fb.toRow === row
            );
            
            if (!hasBlock && !hasBlockFallingToThisPosition) {
                count++;
            }
        }
        return count;
    }
    
    // 找到列中最顶部的空位
    findTopEmptyRow(col) {
        for (let row = 0; row < this.core.gridSizeY; row++) {
            // 检查网格中是否有方块
            const hasBlock = this.core.grid[row][col] && !this.core.grid[row][col].isRemoved;
            
            // 检查是否有方块正在掉落到这个位置
            const hasBlockFallingToThisPosition = this.fallingBlocks.some(
                fb => fb.col === col && fb.toRow === row
            );
            
            if (!hasBlock && !hasBlockFallingToThisPosition) {
                return row;
            }
        }
        return -1; // 列已满
    }
    
    // 更新掉落状态
    update() {
        this.updateFalling();
    }
    
    // 更新掉落动画
    updateFalling() {
        if (this.fallingBlocks.length === 0) {
            return;
        }
        
        const currentTime = Date.now();
        const completedBlocks = [];
        
        this.fallingBlocks.forEach((fallingBlock, index) => {
            const { block, toRow, col, startTime, isNew } = fallingBlock;
            
            // 检查是否到了开始时间
            if (currentTime < startTime) {
                return;
            }
            
            // 更新掉落速度和位置
            block.fallVelocity += this.fallAcceleration;
            // 限制最大掉落速度
            if (block.fallVelocity > 5) {
                block.fallVelocity = 5;
            }
            block.y += block.fallVelocity;
            
            // 检查是否到达目标位置
            if (block.y >= block.targetY) {
                // 到达目标位置
                block.y = block.targetY;
                block.isFalling = false;
                block.fallVelocity = 0;
                
                // 添加落地反弹效果
                block.scale = 1 + this.bounceEffect;
                setTimeout(() => {
                    if (block) block.scale = 1;
                }, 150);
                
                // 将方块放置到网格中，但不覆盖已有方块
                const existingBlock = this.core.grid[toRow][col];
                if (!existingBlock || existingBlock.isRemoved) {
                    // 更新格子的最终位置（确保X坐标正确）
                    const spacing = this.core.getSpacing();
                    block.x = this.core.gridStartX + col * (this.core.blockSize + spacing);
                    block.y = this.core.gridStartY + toRow * (this.core.blockSize + spacing);
                    block.row = toRow;
                    block.col = col;

                    this.core.grid[toRow][col] = block;
                    console.log(`方块放置成功: (${toRow}, ${col}) - ${block.animalType}, X=${block.x}`);
                } else {
                    console.log(`位置(${toRow}, ${col})已有方块，跳过放置 - 现有:${existingBlock.animalType}, 新:${block.animalType}`);
                }
                
                // 标记为完成
                completedBlocks.push(index);
                
                console.log(`方块掉落完成: (${toRow}, ${col})`);
            }
        });
        
        // 移除已完成的掉落方块
        completedBlocks.reverse().forEach(index => {
            this.fallingBlocks.splice(index, 1);
        });
    }
    
    // 渲染掉落中的方块
    renderFallingBlocks(renderer) {
        this.fallingBlocks.forEach(fallingBlock => {
            const { block, col } = fallingBlock;
            
            if (block && Date.now() >= fallingBlock.startTime) {
                // 计算渲染位置
                const x = this.core.gridStartX + col * this.core.blockSize;
                const y = block.y;
                
                // 确保方块在屏幕内才渲染
                if (y < this.canvas.height && y > -this.core.blockSize) {
                    // 渲染掉落中的方块
                    renderer.renderBlock(block, x, y);
                }
            }
        });
    }
    
    // 等待所有掉落完成
    waitForFallingComplete(callback) {
        const checkComplete = () => {
            if (this.fallingBlocks.length === 0) {
                this.isProcessingFall = false;
                console.log('所有方块掉落完成');
                callback();
            } else {
                setTimeout(checkComplete, 50);
            }
        };
        
        checkComplete();
    }
    
    // 检查是否正在处理掉落
    isFalling() {
        return this.isProcessingFall || this.fallingBlocks.length > 0;
    }
    
    // 清理掉落系统
    clear() {
        this.fallingBlocks = [];
        this.isProcessingFall = false;
    }
    
    // 连击系统方法
    
    // 开始连击计数
    startCombo() {
        this.isComboActive = true;
        this.comboCount = 1;
        console.log(`连击开始: ${this.comboCount}`);
    }
    
    // 增加连击数
    incrementCombo() {
        if (this.isComboActive) {
            this.comboCount++;
            if (this.comboCount > this.maxCombo) {
                this.maxCombo = this.comboCount;
            }
            console.log(`连击增加: ${this.comboCount}`);
        }
    }
    
    // 重置连击数
    resetCombo() {
        if (this.isComboActive) {
            console.log(`连击结束: 最高连击 ${this.comboCount}`);
            this.isComboActive = false;
            this.comboCount = 0;
            // 触发重绘，确保提示框立即消失
            if (this.core && this.core.requestRender) {
                this.core.requestRender();
            }
        }
    }
    
    // 获取当前连击数
    getComboCount() {
        return this.comboCount;
    }
    
    // 获取最大连击数
    getMaxCombo() {
        return this.maxCombo;
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageFalling;
} else {
    window.GamePageFalling = GamePageFalling;
}