/**
 * 排行榜页面事件处理模块 - 负责用户交互和事件处理
 */
class RankPageEvents {
    constructor(core, renderer) {
        this.core = core;
        this.renderer = renderer;
        this.canvas = core.canvas;
        this.ctx = core.ctx;
        
        // 按钮配置
        this.buttons = [];
        
        // 事件处理器
        this.touchHandler = null;
        
        this.initButtons();
        console.log('RankPageEvents初始化完成');
    }
    
    // 初始化按钮
    initButtons() {
        const centerX = this.canvas.width / 2;
        const tabWidth = 90;
        const tabSpacing = 10;
        const totalWidth = tabWidth * 3 + tabSpacing * 2;
        const startX = centerX - totalWidth / 2;
        
        this.buttons = [
            {
                id: 'globalRank',
                text: '全部',
                x: startX,
                y: 160,
                width: tabWidth,
                height: 40,
                action: () => this.switchTab('global')
            },
            {
                id: 'regionRank',
                text: '地区',
                x: startX + tabWidth + tabSpacing,
                y: 160,
                width: tabWidth,
                height: 40,
                action: () => this.switchTab('region')
            },
            {
                id: 'friendRank',
                text: '好友',
                x: startX + (tabWidth + tabSpacing) * 2,
                y: 160,
                width: tabWidth,
                height: 40,
                action: () => this.switchTab('friend')
            },
            {
                id: 'back',
                text: '返回',
                x: 20,
                y: 70,
                width: 100,
                height: 32,
                action: () => this.goBack()
            }
        ];
    }
    
    // 绑定事件监听器
    bindEvents() {
        this.touchHandler = (res) => {
            if (res.touches && res.touches.length > 0) {
                const touch = res.touches[0];
                this.handleTouch(touch.clientX, touch.clientY);
            }
        };
        
        if (typeof tt !== 'undefined') {
            tt.onTouchStart(this.touchHandler);
        }
        
        console.log('事件监听器已绑定');
    }
    
    // 解绑事件监听器
    unbindEvents() {
        if (this.touchHandler && typeof tt !== 'undefined') {
            tt.offTouchStart(this.touchHandler);
        }
        
        console.log('事件监听器已解绑');
    }
    
    // 处理触摸事件
    handleTouch(x, y) {
        for (let button of this.buttons) {
            if (this.isPointInButton(x, y, button)) {
                console.log(`点击了按钮: ${button.text}`);
                button.action();
                break;
            }
        }
    }
    
    // 检查点击是否在按钮内
    isPointInButton(x, y, button) {
        return x >= button.x && 
               x <= button.x + button.width && 
               y >= button.y && 
               y <= button.y + button.height;
    }
    
    // 切换标签页
    switchTab(tab) {
        this.core.switchTab(tab);
        this.initButtons(); // 重新初始化按钮状态
    }
    
    // 返回主页面
    goBack() {
        console.log('返回主页面');
        this.core.gameManager.switchToPage('main');
    }
    
    // 渲染所有按钮
    renderButtons() {
        // 绘制标签按钮容器
        const centerX = this.canvas.width / 2;
        const tabY = 160;
        const tabWidth = 280;
        const tabHeight = 40;
        
        this.renderer.renderTabContainer(centerX, tabY, tabWidth, tabHeight);
        
        // 绘制标签按钮
        for (let button of this.buttons) {
            if (button.id === 'globalRank' || button.id === 'regionRank' || button.id === 'friendRank') {
                const isActive = this.isTabActive(button.id);
                this.renderer.renderTabButton(button, isActive);
            }
        }
        
        // 绘制返回按钮
        const backButton = this.buttons.find(btn => btn.id === 'back');
        if (backButton) {
            this.renderer.renderBackButton(
                backButton.x, 
                backButton.y, 
                backButton.width, 
                backButton.height, 
                backButton.text
            );
        }
    }
    
    // 检查标签是否激活
    isTabActive(buttonId) {
        const tabMap = {
            'globalRank': 'global',
            'regionRank': 'region',
            'friendRank': 'friend'
        };
        return this.core.currentTab === tabMap[buttonId];
    }
    
    // 处理键盘事件（如果需要）
    handleKeyboard(keyCode) {
        switch(keyCode) {
            case 37: // 左箭头
                this.switchToPreviousTab();
                break;
            case 39: // 右箭头
                this.switchToNextTab();
                break;
            case 27: // ESC键
                this.goBack();
                break;
        }
    }
    
    // 切换到上一个标签
    switchToPreviousTab() {
        const tabs = ['global', 'region', 'friend'];
        const currentIndex = tabs.indexOf(this.core.currentTab);
        const previousIndex = (currentIndex - 1 + tabs.length) % tabs.length;
        this.switchTab(tabs[previousIndex]);
    }
    
    // 切换到下一个标签
    switchToNextTab() {
        const tabs = ['global', 'region', 'friend'];
        const currentIndex = tabs.indexOf(this.core.currentTab);
        const nextIndex = (currentIndex + 1) % tabs.length;
        this.switchTab(tabs[nextIndex]);
    }
    
    // 获取按钮状态
    getButtonState(buttonId) {
        const button = this.buttons.find(btn => btn.id === buttonId);
        if (!button) return null;
        
        return {
            id: button.id,
            text: button.text,
            x: button.x,
            y: button.y,
            width: button.width,
            height: button.height,
            isActive: this.isTabActive(buttonId)
        };
    }
    
    // 更新按钮位置（响应式布局）
    updateButtonLayout() {
        this.initButtons();
    }
    
    // 初始化事件处理
    init() {
        this.bindEvents();
    }
    
    // 销毁事件处理
    destroy() {
        this.unbindEvents();
        this.buttons = [];
        console.log('RankPageEvents已销毁');
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RankPageEvents;
} else {
    window.RankPageEvents = RankPageEvents;
}
