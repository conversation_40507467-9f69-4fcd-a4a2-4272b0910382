// 导入GamePageCore类
const GamePageCore = require('./GamePageCore');

// 游戏页面事件处理器 - 负责触摸和交互事件
class GamePageEvents {
    constructor(gameCore, gameRenderer) {
        this.core = gameCore;
        this.renderer = gameRenderer;
        this.canvas = gameCore.canvas;
        
        // 触摸状态
        this.isDragging = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
        this.lastTouchTime = 0;
        this.touchStartPos = { x: 0, y: 0 };

        // 炸弹卡拖拽状态
        this.isDraggingBomb = false;
        this.bombDragStartX = -1;
        this.bombDragStartY = -1;
        this.bombDragCurrentX = -1;
        this.bombDragCurrentY = -1;
        
        // 事件处理器引用
        this.touchStartHandler = null;
        this.touchMoveHandler = null;
        this.touchEndHandler = null;
        
        console.log('GamePageEvents初始化完成');
    }
    
    // 初始化事件监听
    init() {
        this.setupTouchEvents();
        console.log('游戏事件监听器设置完成');
    }
    
    // 设置触摸事件监听
    setupTouchEvents() {
        if (typeof tt !== 'undefined') {
            console.log('使用抖音小游戏触摸事件API');
            
            // 触摸开始事件
            this.touchStartHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchStart(touch.clientX, touch.clientY);
                }
            };
            
            // 触摸移动事件
            this.touchMoveHandler = (e) => {
                if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    this.handleTouchMove(touch.clientX, touch.clientY);
                }
            };
            
            // 触摸结束事件
            this.touchEndHandler = (e) => {
                this.handleTouchEnd();
            };
            
            // 注册事件监听器
            tt.onTouchStart(this.touchStartHandler);
            tt.onTouchMove(this.touchMoveHandler);
            tt.onTouchEnd(this.touchEndHandler);
            
            console.log('抖音小游戏触摸事件监听器设置完成');
        } else {
            console.warn('非抖音小游戏环境，跳过触摸事件设置');
        }
    }
    
    // 处理触摸开始事件
    handleTouchStart(x, y) {
        this.touchStartPos = { x, y };
        this.lastTouchTime = Date.now();
        
        // 检查按钮点击
        if (this.handleButtonClick(x, y)) {
            return;
        }

        // 检查道具栏点击
        if (this.handlePropBarClick(x, y)) {
            return;
        }

        // 检查游戏网格点击
        this.handleGridTouchStart(x, y);
    }
    
    // 处理触摸移动事件
    handleTouchMove(x, y) {
        if (this.isDragging) {
            this.handleGridTouchMove(x, y);
        } else if (this.isDraggingBomb) {
            // 更新炸弹卡拖拽位置
            this.bombDragCurrentX = x;
            this.bombDragCurrentY = y;
        }
    }
    
    // 处理触摸结束事件
    handleTouchEnd(x, y) {
        if (this.isDragging) {
            this.handleGridTouchEnd();
        } else if (this.isDraggingBomb) {
            // 处理炸弹卡拖拽结束
            this.handleBombDragEnd(x, y);
        }

        // 重置拖拽状态
        this.isDragging = false;
        this.isDraggingBomb = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
    }

    // 处理炸弹卡拖拽结束
    handleBombDragEnd(x, y) {
        const gridPos = this.getGridPosition(x, y);

        if (gridPos.row >= 0 && gridPos.col >= 0) {
            // 在网格内释放，使用炸弹卡
            console.log(`尝试在位置(${gridPos.row}, ${gridPos.col})使用炸弹卡`);
            if (this.core.useBombPropAt(gridPos.row, gridPos.col)) {
                console.log(`炸弹卡使用成功: (${gridPos.row}, ${gridPos.col})`);
            } else {
                console.log('炸弹卡使用失败');
            }
        } else {
            console.log('炸弹卡拖拽取消 - 不在网格内');
        }

        // 重置炸弹拖拽状态（道具使用状态在useBombPropAt中重置）
        this.isDraggingBomb = false;
        this.bombDragStartX = -1;
        this.bombDragStartY = -1;
        this.bombDragCurrentX = -1;
        this.bombDragCurrentY = -1;

        // 如果没有成功使用炸弹卡，重置道具使用状态
        if (this.core.propUsing.isActive && this.core.propUsing.type === 'bomb') {
            console.log('炸弹卡拖拽取消，重置道具使用状态');
            this.core.propUsing.isActive = false;
            this.core.propUsing.type = null;
        }
    }
    
    // 处理按钮点击
    handleButtonClick(x, y) {
        const buttons = this.renderer.getButtons();
        
        // 如果显示退出弹框，优先处理弹框按钮
        if (this.core.showExitDialog) {
            // 检查继续游戏按钮
            if (buttons.continueGame && this.renderer.isPointInButton(x, y, buttons.continueGame)) {
                console.log('点击继续游戏按钮');
                this.core.showExitDialog = false;
                return true;
            }
            
            // 检查返回主页按钮
            if (buttons.exitGame && this.renderer.isPointInButton(x, y, buttons.exitGame)) {
                console.log('点击返回主页按钮');
                this.core.showExitDialog = false;
                this.core.gameManager.switchToPage('main');
                return true;
            }
            
            // 点击弹框外部区域，关闭弹框
            const centerX = this.canvas.width / 2;
            const centerY = this.canvas.height / 2;
            const dialogWidth = 300;
            const dialogHeight = 180;
            const dialogX = centerX - dialogWidth / 2;
            const dialogY = centerY - dialogHeight / 2;
            
            if (x < dialogX || x > dialogX + dialogWidth || y < dialogY || y > dialogY + dialogHeight) {
                console.log('点击弹框外部，关闭弹框');
                this.core.showExitDialog = false;
                return true;
            }
            
            return true; // 弹框显示时阻止其他操作
        }
        
        // 检查返回按钮
        if (buttons.back && this.renderer.isPointInButton(x, y, buttons.back)) {
            console.log('点击返回按钮，显示确认弹框');
            this.core.showExitDialog = true;
            return true;
        }
        
        return false;
    }

    // 处理道具栏点击
    handlePropBarClick(x, y) {
        const gridBottom = this.core.gridStartY + this.core.gridSizeY * this.core.blockSize;
        const propBarY = gridBottom + 20;
        const propSize = 60;
        const propSpacing = 80;
        const startX = (this.canvas.width - (propSpacing * 2)) / 2;

        // 检查是否在道具栏区域内
        if (y < propBarY || y > propBarY + propSize) {
            return false;
        }

        // 道具配置
        const props = [
            { type: 'refresh', count: this.core.props.refresh },
            { type: 'bomb', count: this.core.props.bomb },
            { type: 'clear', count: this.core.props.clear }
        ];

        for (let i = 0; i < props.length; i++) {
            const propX = startX + i * propSpacing;

            if (x >= propX && x <= propX + propSize && props[i].count > 0) {
                console.log(`点击道具: ${props[i].type}`);

                // 炸弹卡需要拖拽，其他道具直接使用
                if (props[i].type === 'bomb') {
                    // 开始拖拽炸弹卡
                    this.startBombDrag(x, y);
                } else {
                    this.useProp(props[i].type);
                }
                return true;
            }
        }

        return false;
    }

    // 开始拖拽炸弹卡
    startBombDrag(x, y) {
        if (this.core.props.bomb <= 0) {
            console.log('炸弹卡数量不足');
            return;
        }

        // 激活炸弹卡使用状态
        this.core.propUsing.isActive = true;
        this.core.propUsing.type = 'bomb';

        this.isDraggingBomb = true;
        this.bombDragStartX = x;
        this.bombDragStartY = y;
        this.bombDragCurrentX = x;
        this.bombDragCurrentY = y;

        console.log('开始拖拽炸弹卡，已激活使用状态');
    }

    // 使用道具
    useProp(propType) {
        switch (propType) {
            case 'refresh':
                if (this.core.useRefreshProp()) {
                    console.log('刷新卡使用成功');
                    // 播放道具音效
                    if (this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('prop');
                    }
                }
                break;

            case 'clear':
                if (this.core.useClearProp()) {
                    console.log('清屏卡使用成功');
                    // 播放道具音效
                    if (this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('prop');
                    }
                }
                break;

            case 'bomb':
                if (this.core.activateBombProp()) {
                    console.log('炸弹卡已激活，等待选择位置');
                    // 播放道具音效
                    if (this.core.gameManager.audioManager) {
                        this.core.gameManager.audioManager.playSound('prop');
                    }
                }
                break;

            default:
                console.warn('未知道具类型:', propType);
        }
    }
    
    // 处理网格触摸开始
    handleGridTouchStart(x, y) {
        if (this.core.isAnimating || this.core.isGameOver || this.core.isLevelComplete || this.core.showExitDialog) {
            return;
        }

        const gridPos = this.getGridPosition(x, y);
        if (gridPos.row >= 0 && gridPos.col >= 0) {
            // 检查是否正在使用炸弹卡
            if (this.core.propUsing.isActive && this.core.propUsing.type === 'bomb') {
                // 使用炸弹卡在指定位置
                this.core.useBombPropAt(gridPos.row, gridPos.col);
                return;
            }

            this.isDragging = true;
            this.dragStartRow = gridPos.row;
            this.dragStartCol = gridPos.col;

            // 重置连击计数 - 用户开始新的拖动操作时重置
            this.core.resetCombo();

            // 选中方块
            this.selectBlock(gridPos.row, gridPos.col);

            console.log(`选中方块: (${gridPos.row}, ${gridPos.col})`);
        }
    }
    
    // 处理网格触摸移动
    handleGridTouchMove(x, y) {
        const gridPos = this.getGridPosition(x, y);
        if (gridPos.row >= 0 && gridPos.col >= 0) {
            this.dragEndRow = gridPos.row;
            this.dragEndCol = gridPos.col;
            
            // 检查是否移动到相邻方块
            if (this.isAdjacentBlock(this.dragStartRow, this.dragStartCol, gridPos.row, gridPos.col)) {
                // 高亮目标方块
                this.highlightTargetBlock(gridPos.row, gridPos.col);
            }
        }
    }
    
    // 处理网格触摸结束
    handleGridTouchEnd() {
        if (this.dragStartRow >= 0 && this.dragStartCol >= 0 && 
            this.dragEndRow >= 0 && this.dragEndCol >= 0) {
            
            // 检查是否是有效的交换
            if (this.isAdjacentBlock(this.dragStartRow, this.dragStartCol, this.dragEndRow, this.dragEndCol)) {
                this.attemptSwap(
                    this.dragStartRow, this.dragStartCol,
                    this.dragEndRow, this.dragEndCol
                );
            } else if (this.dragStartRow === this.dragEndRow && this.dragStartCol === this.dragEndCol) {
                // 单击选择/取消选择
                this.handleBlockClick(this.dragStartRow, this.dragStartCol);
            }
        }
        
        // 清除所有选中状态
        this.clearAllSelections();
    }
    
    // 获取网格位置
    getGridPosition(x, y) {
        const col = Math.floor((x - this.core.gridStartX) / this.core.blockSize);
        const row = Math.floor((y - this.core.gridStartY) / this.core.blockSize);
        
        if (row >= 0 && row < this.core.gridSizeY && col >= 0 && col < this.core.gridSizeX) {
            return { row, col };
        }
        
        return { row: -1, col: -1 };
    }
    
    // 检查是否是相邻方块
    isAdjacentBlock(row1, col1, row2, col2) {
        const rowDiff = Math.abs(row1 - row2);
        const colDiff = Math.abs(col1 - col2);
        
        return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
    }
    
    // 选中方块
    selectBlock(row, col) {
        if (this.core.grid[row] && this.core.grid[row][col]) {
            // 清除之前的选中状态
            this.clearAllSelections();
            
            // 选中当前方块
            this.core.grid[row][col].isSelected = true;
            this.core.selectedBlock = { row, col };
            
            // 创建选中特效
            const block = this.core.grid[row][col];
            this.core.createSparkles(
                block.x + this.core.blockSize / 2,
                block.y + this.core.blockSize / 2,
                3
            );
        }
    }
    
    // 高亮目标方块
    highlightTargetBlock(row, col) {
        if (this.core.grid[row] && this.core.grid[row][col]) {
            this.core.grid[row][col].isSelected = true;
        }
    }
    
    // 清除所有选中状态
    clearAllSelections() {
        for (let row = 0; row < this.core.gridSizeY; row++) {
            for (let col = 0; col < this.core.gridSizeX; col++) {
                if (this.core.grid[row] && this.core.grid[row][col]) {
                    this.core.grid[row][col].isSelected = false;
                }
            }
        }
        this.core.selectedBlock = null;
    }
    
    // 处理方块点击
    handleBlockClick(row, col) {
        if (this.core.selectedBlock) {
            // 如果已经有选中的方块，尝试交换
            if (this.core.selectedBlock.row !== row || this.core.selectedBlock.col !== col) {
                if (this.isAdjacentBlock(this.core.selectedBlock.row, this.core.selectedBlock.col, row, col)) {
                    this.attemptSwap(this.core.selectedBlock.row, this.core.selectedBlock.col, row, col);
                } else {
                    // 选中新的方块
                    this.selectBlock(row, col);
                }
            } else {
                // 取消选中
                this.clearAllSelections();
            }
        } else {
            // 选中方块
            this.selectBlock(row, col);
        }
    }
    
    // 尝试交换方块 - 支持特殊方块交换
    async attemptSwap(row1, col1, row2, col2) {
        console.log(`尝试交换方块: (${row1}, ${col1}) <-> (${row2}, ${col2})`);

        const block1 = this.core.grid[row1] && this.core.grid[row1][col1];
        const block2 = this.core.grid[row2] && this.core.grid[row2][col2];

        if (!block1 || !block2) {
            return false;
        }

        // 检查是否是特殊方块交换
        const hasSpecialBlock = (block1.blockType === 'special') || (block2.blockType === 'special');

        if (hasSpecialBlock) {
            // 处理特殊方块交换
            const specialResult = this.core.processSpecialSwap(block1, block2, row1, col1, row2, col2);

            // 检查交换是否有效
            if (specialResult.invalid) {
                console.log('特殊方块交换无效，被交换的格子不满足消除条件');
                return false;
            }

            if (specialResult.score > 0) {
                // 增加分数
                this.core.score += specialResult.score;
                this.core.addFloatingText(`+${specialResult.score}`,
                    (col1 + col2) * this.core.blockSize / 2 + this.core.gridStartX + this.core.blockSize/2,
                    (row1 + row2) * this.core.blockSize / 2 + this.core.gridStartY + this.core.blockSize/2,
                    '#FFD700');

                // 对于火箭交换，eliminateColumn已经清除了格子，不需要重复清除
                const isRocketSwap = (block1.special === 'rocket' && block2.blockType === 'normal') ||
                                   (block2.special === 'rocket' && block1.blockType === 'normal');

                if (!isRocketSwap) {
                    // 清除被消除的方块（非火箭交换）
                    specialResult.eliminatedBlocks.forEach(item => {
                        this.core.grid[item.row][item.col] = null;
                    });
                }

                // 对于火箭交换，不需要清除交换的格子，因为已经在processSpecialSwap中处理了交换
                // 只有在其他特殊交换情况下才清除交换的格子

                if (!isRocketSwap) {
                    // 清除交换的特殊方块（非火箭交换）
                    this.core.grid[row1][col1] = null;
                    this.core.grid[row2][col2] = null;
                }

                // 处理掉落
                setTimeout(() => {
                    if (this.core.animator && this.core.animator.falling) {
                        const removedPositions = specialResult.eliminatedBlocks.map(item => ({ row: item.row, col: item.col }));

                        // 对于火箭交换，不添加交换位置到removedPositions，因为这些位置没有被清除
                        if (!isRocketSwap) {
                            removedPositions.push({ row: row1, col: col1 }, { row: row2, col: col2 });
                        }

                        console.log(`特殊交换下落处理: 火箭交换=${isRocketSwap}, 移除位置数=${removedPositions.length}`);
                        this.core.animator.falling.processFalling(removedPositions);
                    }
                }, 300);

                return true;
            }
        }

        // 普通方块交换逻辑
        // 执行交换
        this.swapBlocks(row1, col1, row2, col2);

        // 使用动画控制器处理交换结果
        if (this.core.animator) {
            const swapResult = {
                success: true,
                from: { row: row1, col: col1 },
                to: { row: row2, col: col2 }
            };

            const result = await this.core.animator.processSwapResult(swapResult);

            if (!result) {
                // 没有匹配，交换回来
                setTimeout(() => {
                    this.swapBlocks(row1, col1, row2, col2);
                    console.log('无效交换，已还原');
                }, 300);
                return false;
            }

            return true;
        } else {
            // 回退到原来的逻辑
            const matchResult = this.core.checkForMatches();

            if (matchResult && matchResult.matches.length > 0) {
                this.handleMatches(matchResult.matches);
                return true;
            } else {
                setTimeout(() => {
                    this.swapBlocks(row1, col1, row2, col2);
                    console.log('无效交换，已还原');
                }, 300);
                return false;
            }
        }
    }
    
    // 交换方块
    swapBlocks(row1, col1, row2, col2) {
        const temp = { ...this.core.grid[row1][col1] };
        
        // 交换属性但保持位置
        const pos1 = { x: this.core.grid[row1][col1].x, y: this.core.grid[row1][col1].y };
        const pos2 = { x: this.core.grid[row2][col2].x, y: this.core.grid[row2][col2].y };
        
        this.core.grid[row1][col1] = { ...this.core.grid[row2][col2], ...pos1 };
        this.core.grid[row2][col2] = { ...temp, ...pos2 };
        
        // 添加交换动画效果
        this.addSwapAnimation(row1, col1, row2, col2);
    }
    
    // 添加交换动画
    addSwapAnimation(row1, col1, row2, col2) {
        // 简单的缩放动画
        const block1 = this.core.grid[row1][col1];
        const block2 = this.core.grid[row2][col2];
        
        if (block1 && block2) {
            block1.scale = 1.2;
            block2.scale = 1.2;
            
            setTimeout(() => {
                if (block1) block1.scale = 1;
                if (block2) block2.scale = 1;
            }, 200);
        }
    }
    
    // 处理匹配
    handleMatches(matches) {
        console.log(`发现 ${matches.length} 个匹配`);
        
        this.core.isAnimating = true;
        
        // 计算分数
        const totalScore = matches.length * 10;
        this.core.score += totalScore;
        // 更新全局静态参数
        GamePageCore.score += totalScore;
        // 计算进度值
        GamePageCore.progress = Math.min(100, Math.floor((GamePageCore.score / GamePageCore.targetScore) * 100));
        
        // 创建特效
        matches.forEach(match => {
            const block = this.core.grid[match.row][match.col];
            if (block) {
                // 粒子效果
                this.core.createParticles(
                    block.x + this.core.blockSize / 2,
                    block.y + this.core.blockSize / 2,
                    block.color,
                    8
                );
                
                // 闪烁效果
                this.core.createSparkles(
                    block.x + this.core.blockSize / 2,
                    block.y + this.core.blockSize / 2,
                    5
                );
            }
        });
        
        // 显示得分文字
        this.core.createFloatingText(
            this.canvas.width / 2,
            this.canvas.height / 2,
            `+${totalScore}`,
            '#FFD700'
        );
        
        // 移除匹配的方块
        setTimeout(() => {
            this.removeMatches(matches);
        }, 300);
    }
    
    // 移除匹配的方块
    removeMatches(matches) {
        matches.forEach(match => {
            if (this.core.grid[match.row] && this.core.grid[match.row][match.col]) {
                // 保存原始位置
                const originalX = this.core.grid[match.row][match.col].x;
                const originalY = this.core.grid[match.row][match.col].y;
                
                // 使用核心类的方法创建新方块
                const newBlock = this.core.createRandomBlock(
                    match.row, 
                    match.col, 
                    originalX - match.col * this.core.blockSize,
                    originalY - match.row * this.core.blockSize
                );
                
                if (newBlock) {
                    // 设置初始动画状态
                    newBlock.scale = 0.5;
                    newBlock.alpha = 0.8;
                    
                    this.core.grid[match.row][match.col] = newBlock;
                    
                    // 添加出现动画
                    let scale = 0.5;
                    const scaleInterval = setInterval(() => {
                        scale += 0.1;
                        if (newBlock) newBlock.scale = scale;
                        if (scale >= 1) {
                            clearInterval(scaleInterval);
                            if (newBlock) newBlock.scale = 1;
                        }
                    }, 50);
                }
            }
        });
        
        // 检查是否还有新的匹配
        setTimeout(() => {
            const newMatches = this.core.checkForMatches();
            if (newMatches.length > 0) {
                this.handleMatches(newMatches);
            } else {
                this.core.isAnimating = false;
                this.core.combo = 0; // 重置连击
            }
        }, 500);
    }
    
    // 处理暂停游戏
    handlePauseGame() {
        // 这里可以添加暂停游戏的逻辑
        console.log('游戏暂停功能待实现');
    }
    
    // 清理事件监听器
    destroy() {
        console.log('清理GamePageEvents资源');
        
        if (typeof tt !== 'undefined') {
            if (this.touchStartHandler) {
                tt.offTouchStart(this.touchStartHandler);
            }
            if (this.touchMoveHandler) {
                tt.offTouchMove(this.touchMoveHandler);
            }
            if (this.touchEndHandler) {
                tt.offTouchEnd(this.touchEndHandler);
            }
            console.log('抖音小游戏事件监听器已清理');
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GamePageEvents };
} else {
    window.GamePageEvents = GamePageEvents;
}