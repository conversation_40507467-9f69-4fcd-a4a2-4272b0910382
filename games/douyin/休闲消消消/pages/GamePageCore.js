// GamePage核心类 - 基础功能和初始化（超美化版本）
class GamePageCore {
    
    // 获取配置（使用全局配置）
    static getConfig() {
        const hasConfig = typeof GAME_CONFIG !== 'undefined';
        console.log('配置检查:', hasConfig ? '已加载' : '未加载');

        if (hasConfig) {
            console.log('使用全局配置:', GAME_CONFIG);
            return GAME_CONFIG;
        } else {
            console.log('使用默认配置');
            return {
                GRID: { SIZE_X: 8, SIZE_Y: 10, BLOCK_SIZE: 45, START_Y: 280, MARGIN_TOP: 50 },
                LAYOUT: { UI_OFFSET_Y: 120, HEADER_HEIGHT: 120, SCORE_AREA_Y: 140, LEVEL_INFO_Y: 180, PROGRESS_BAR_Y: 210, PROPS_AREA_Y: 100, PROPS_BUTTON_SIZE: 60, PROPS_SPACING: 80, BACK_BUTTON_Y: 30, BACK_BUTTON_HEIGHT: 32, BOTTOM_AREA_Y: 750, BUTTON_HEIGHT: 50, BUTTON_SPACING: 20, DIALOG_WIDTH: 400, DIALOG_HEIGHT: 300 },
                ANIMATION: { PARTICLE_COUNT: 8, SPARKLE_COUNT: 3 },
                COLORS: { TEXT_PRIMARY: '#FFFFFF', TEXT_SECONDARY: '#E0E0E0', BUTTON_PRIMARY: '#4CAF50', BUTTON_SECONDARY: '#2196F3', BUTTON_DANGER: '#F44336' },
                GAME: { ANIMATION_SPEED: 0.016 }
            };
        }
    }
    
    constructor(gameManager, levelConfig) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        
        // 设置关卡配置
        this.levelConfig = levelConfig || {
            targetScore: 1000,
            name: '萌宠新手村',
            level: 1
        };
        
        // 初始化游戏数据
        this.score = 0;

        // 从配置中获取游戏参数
        this.targetScore = this.levelConfig.targetScore;
        this.levelName = this.levelConfig.name;
        this.level = this.levelConfig.level;

        // 将分数和进度设为全局变量方便直接计算和赋值
        window.gameScore = this.score;
        window.gameTargetScore = this.targetScore;
        window.gameProgress = 0;
        
        // 网格配置（使用全局配置和动态计算）
        const config = GamePageCore.getConfig();
        this.gridSizeX = config.GRID.SIZE_X;
        this.gridSizeY = config.GRID.SIZE_Y;

        // 动态计算格子大小
        if (typeof CONFIG_UTILS !== 'undefined') {
            const gridConfig = CONFIG_UTILS.getGridConfig(this.canvas.width, this.canvas.height);
            this.blockSize = gridConfig.blockSize;
            console.log(`动态计算格子大小: ${this.blockSize}px`);
        } else {
            this.blockSize = 45; // 回退到默认值
            console.log('使用默认格子大小: 45px');
        }
        
        // 根据关卡配置萌宠类型
        this.initAnimalTypes();
        this.animalImages = {};
        this.propImages = {};

        // 使用全局配置的颜色
        this.animalColors = config.COLORS ? config.COLORS.ANIMALS : {
            'cat': '#FF6B9D', 'dog': '#4ECDC4', 'elephant': '#45B7D1', 'fox': '#96CEB4',
            'frog': '#FFEAA7', 'monkey': '#DDA0DD', 'panda': '#98D8C8', 'rabbit': '#F7DC6F', 'tiger': '#BB8FCE'
        };

        // 特殊方块类型（使用全局配置）
        this.specialTypes = config.SPECIAL_BLOCKS ? config.SPECIAL_BLOCKS.TYPES : {
            ROCKET: 'rocket',
            BOMB: 'bomb'
        };

        // 特殊方块颜色（使用全局配置）
        this.specialColors = config.COLORS ? config.COLORS.SPECIAL : {
            'rocket': '#FF4500',
            'bomb': '#8B0000'
        };
        
        // 拖拽相关状态
        this.isDragging = false;
        this.dragStartRow = -1;
        this.dragStartCol = -1;
        this.dragEndRow = -1;
        this.dragEndCol = -1;
        this.dragStartX = -1;
        this.dragStartY = -1;
        this.dragCurrentX = -1;
        this.dragCurrentY = -1;
        
        // 游戏状态
        this.score = 0;
        this.isAnimating = false;
        this.selectedBlock = null;
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.reviveCount = 0;
        this.isBombCardSelecting = false;
        this.showExitDialog = false;
        
        // 计分规则
        this.scoreRules = {
            match3: 20,
            match4: 30,
            match5: 50,
            rocketCombo: 100,
            bombCombo: 150,
            rocketBombCombo: 300,
            bombBombCombo: 500,
            comboMultipliers: [1.0, 1.5, 2.0] // 1次、2次、3次及以上连击倍率
        };

        // 连击系统
        this.combo = 0;
        this.maxCombo = 0;
        this.comboMultiplier = 1.0;
        this.comboCount = 0; // 连续消除次数
        this.comboTimer = 0; // 连击计时器
        
        // 动画和效果
        this.fallingBlocks = [];
        this.particles = [];
        this.sparkles = [];
        this.floatingTexts = [];
        this.animations = [];
        this.animationTime = 0;
        
        // 道具系统
        this.props = {
            refresh: 3,  // 刷新卡数量
            bomb: 2,     // 炸弹卡数量
            clear: 1     // 清屏卡数量
        };

        // 道具使用状态
        this.propUsing = {
            type: null,     // 当前使用的道具类型
            isActive: false // 是否正在使用道具
        };
        
        // 音效管理器
        this.audioManager = gameManager.audioManager;
        
        // 背景效果
        this.backgroundStars = null;
        
        // 动画控制器
        this.animator = null;

        // 事件处理器
        this.events = null;

        console.log('GamePageCore初始化完成');
    }

    /**
     * 更新分数并同步全局变量
     * @param {number} points - 要增加的分数
     */
    updateScore(points) {
        this.score += points;
        window.gameScore = this.score;
        window.gameProgress = Math.min(this.score / this.targetScore, 1.0);
        console.log(`分数更新: +${points}, 总分: ${this.score}, 进度: ${(window.gameProgress * 100).toFixed(1)}%`);
    }

    /**
     * 重置分数并同步全局变量
     */
    resetScore() {
        this.score = 0;
        window.gameScore = this.score;
        window.gameProgress = 0;
        console.log('分数已重置');
    }

    // 根据关卡初始化萌宠类型（使用全局配置）
    initAnimalTypes() {
        const config = GamePageCore.getConfig();
        const allAnimals = config.ANIMALS ? config.ANIMALS.TYPES :
            ['cat', 'dog', 'elephant', 'fox', 'frog', 'monkey', 'panda', 'rabbit', 'tiger'];

        // 使用关卡配置确定萌宠数量
        let animalCount;
        if (config.LEVELS) {
            const levelConfig = config.LEVELS.find(l => l.id === this.level);
            animalCount = levelConfig ? levelConfig.animalCount : 5;
        } else {
            // 回退到原有逻辑
            switch (this.level) {
                case 1: animalCount = 5; break;
                case 2: animalCount = 7; break;
                case 3:
                default: animalCount = 9; break;
            }
        }

        // 随机选择指定数量的萌宠类型
        this.animalTypes = allAnimals.slice(0, animalCount);
        console.log(`关卡${this.level}使用${animalCount}种萌宠:`, this.animalTypes);
    }

    // 获取格子间距
    getSpacing() {
        const config = GamePageCore.getConfig();
        return config.GRID ? config.GRID.SPACING : 2;
    }

    /**
     * 公用的格子填充方法
     * @param {Array} positions - 需要填充的位置数组 [{row, col}, ...]
     * @param {Object} options - 填充选项
     * @param {string} options.fillOrder - 填充顺序: 'BOTTOM_UP'(从下往上) 或 'TOP_DOWN'(从上往下)
     * @param {number} options.startRow - 新格子的起始行（默认为-1，即网格上方）
     * @param {number} options.fallSpeed - 下落速度（默认使用falling模块的速度）
     * @returns {Array} 创建的新格子数组
     */
    fillBlocks(positions, options = {}) {
        if (!positions || positions.length === 0) {
            return [];
        }

        const {
            fillOrder = 'BOTTOM_UP',
            startRow = -1,
            fallSpeed = null
        } = options;

        console.log(`公用填充方法: 填充${positions.length}个位置, 顺序=${fillOrder}`);

        // 根据填充顺序排序位置
        let sortedPositions;
        if (fillOrder === 'BOTTOM_UP') {
            // 从下往上：按行号降序排列
            sortedPositions = [...positions].sort((a, b) => b.row - a.row);
        } else {
            // 从上往下：按行号升序排列
            sortedPositions = [...positions].sort((a, b) => a.row - b.row);
        }

        const newBlocks = [];
        const spacing = this.getSpacing();

        sortedPositions.forEach((pos, index) => {
            const { row, col } = pos;

            // 创建新格子（传递正确的起始位置）
            const newBlock = this.createRandomBlock(row, col, this.gridStartX, this.gridStartY);
            if (newBlock) {
                // 设置初始位置（在网格上方）
                newBlock.y = this.gridStartY + startRow * (this.blockSize + spacing);
                newBlock.targetY = this.gridStartY + row * (this.blockSize + spacing);
                newBlock.isFalling = true;
                newBlock.targetRow = row;
                newBlock.isNewBlock = true;

                // 设置下落速度
                if (fallSpeed !== null) {
                    newBlock.fallVelocity = fallSpeed;
                } else if (this.falling) {
                    newBlock.fallVelocity = this.falling.fallSpeed;
                } else {
                    newBlock.fallVelocity = 5; // 默认速度
                }

                // 添加到网格和下落列表
                this.grid[row][col] = newBlock;
                if (this.falling && this.falling.fallingBlocks) {
                    this.falling.fallingBlocks.push(newBlock);
                }

                newBlocks.push(newBlock);

                console.log(`填充位置(${row},${col}): 起始Y=${newBlock.y}, 目标Y=${newBlock.targetY}`);
            }
        });

        console.log(`公用填充完成: 创建了${newBlocks.length}个新格子`);
        return newBlocks;
    }
    
    // 初始化游戏网格
    initGrid() {
        console.log('开始初始化游戏网格');

        this.grid = [];

        // 使用新的布局配置
        const startX = this.gridStartX;
        const startY = this.gridStartY;

        console.log(`网格配置: ${this.gridSizeX}x${this.gridSizeY}, 起始位置: (${startX}, ${startY})`);

        for (let row = 0; row < this.gridSizeY; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.createRandomBlock(row, col, startX, startY);
                this.grid[row][col] = block;
            }
        }

        console.log('游戏网格初始化完成');
        this.removeInitialMatches();
    }
    
    // 创建随机方块（增强版本）
    createRandomBlock(row = 0, col = 0, startX = 0, startY = 0, forceType = null) {
        if (!this.animalTypes || this.animalTypes.length === 0) {
            console.error('animalTypes未初始化');
            return null;
        }

        let type, color, blockType;

        if (forceType) {
            // 强制指定类型（用于生成特殊方块）
            type = forceType;
            if (forceType === 'rocket' || forceType === 'bomb') {
                blockType = 'special';
                color = this.specialColors[forceType] || '#FF4500';
            } else {
                blockType = 'normal';
                color = this.animalColors[forceType] || '#FF6B9D';
            }
        } else {
            // 普通随机生成
            type = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];
            blockType = 'normal';
            color = this.animalColors[type] || '#FF6B9D';
        }

        return {
            // 基本属性
            type: type,
            color: color,

            // 格子分类：'normal' 或 'special'
            category: blockType,

            // 特殊格子类型：null, 'rocket', 'bomb' 等
            specialType: blockType === 'special' ? type : null,

            // 位置信息（考虑间距）
            x: startX + col * (this.blockSize + this.getSpacing()),
            y: startY + row * (this.blockSize + this.getSpacing()),
            row: row,
            col: col,

            // 动画属性
            scale: 1,
            alpha: 1,
            rotation: 0,
            animationOffset: Math.random() * Math.PI * 2,

            // 状态
            isSelected: false,
            isMatched: false,
            isFalling: false,

            // 特效
            glowIntensity: 0,
            pulsePhase: Math.random() * Math.PI * 2
        };
    }

    // 创建特殊方块
    createSpecialBlock(row, col, startX, startY, specialType) {
        return this.createRandomBlock(row, col, startX, startY, specialType);
    }
    
    // 移除初始匹配 - 增强版，确保不出现3个连在一起
    removeInitialMatches() {
        let hasMatches = true;
        let attempts = 0;
        const maxAttempts = 100; // 增加最大尝试次数

        while (hasMatches && attempts < maxAttempts) {
            const matchResult = this.checkForMatches();
            if (!matchResult || matchResult.matches.length === 0) {
                hasMatches = false;
            } else {
                // 替换匹配的方块
                matchResult.matches.forEach(match => {
                    let newType;
                    let validType = false;
                    let typeAttempts = 0;

                    // 尝试找到一个不会产生新匹配的类型
                    while (!validType && typeAttempts < 20) {
                        newType = this.animalTypes[Math.floor(Math.random() * this.animalTypes.length)];

                        // 临时设置新类型
                        const originalType = this.grid[match.row][match.col].type;
                        this.grid[match.row][match.col].type = newType;
                        this.grid[match.row][match.col].color = this.animalColors[newType];

                        // 检查是否还会产生匹配
                        if (!this.wouldCreateMatch(match.row, match.col, newType)) {
                            validType = true;
                        } else {
                            // 恢复原类型继续尝试
                            this.grid[match.row][match.col].type = originalType;
                            typeAttempts++;
                        }
                    }

                    // 如果找不到合适的类型，使用随机类型
                    if (!validType) {
                        const newBlock = this.createRandomBlock(
                            match.row,
                            match.col,
                            this.gridStartX,
                            this.gridStartY
                        );
                        this.grid[match.row][match.col] = newBlock;
                    }
                });
            }
            attempts++;
        }

        if (attempts >= maxAttempts) {
            console.warn('移除初始匹配达到最大尝试次数');
        }

        console.log(`初始化完成，尝试${attempts}次移除匹配`);
    }

    // 检查指定位置放置指定类型是否会产生匹配
    wouldCreateMatch(row, col, type) {
        // 检查水平方向
        let horizontalCount = 1;

        // 向左检查
        for (let c = col - 1; c >= 0; c--) {
            if (this.grid[row][c] && this.grid[row][c].type === type && this.grid[row][c].blockType === 'normal') {
                horizontalCount++;
            } else {
                break;
            }
        }

        // 向右检查
        for (let c = col + 1; c < this.gridSizeX; c++) {
            if (this.grid[row][c] && this.grid[row][c].type === type && this.grid[row][c].blockType === 'normal') {
                horizontalCount++;
            } else {
                break;
            }
        }

        if (horizontalCount >= 3) return true;

        // 检查垂直方向
        let verticalCount = 1;

        // 向上检查
        for (let r = row - 1; r >= 0; r--) {
            if (this.grid[r][col] && this.grid[r][col].type === type && this.grid[r][col].blockType === 'normal') {
                verticalCount++;
            } else {
                break;
            }
        }

        // 向下检查
        for (let r = row + 1; r < this.gridSizeY; r++) {
            if (this.grid[r][col] && this.grid[r][col].type === type && this.grid[r][col].blockType === 'normal') {
                verticalCount++;
            } else {
                break;
            }
        }

        if (verticalCount >= 3) return true;

        return false;
    }

    // 道具系统方法

    // 使用刷新卡
    useRefreshProp() {
        if (this.props.refresh <= 0) {
            console.log('刷新卡数量不足');
            return false;
        }

        this.props.refresh--;

        // 使用打乱逻辑而不是重新生成
        this.shuffleGrid();

        // 确保没有初始匹配
        this.removeInitialMatches();

        console.log('使用刷新卡，剩余数量:', this.props.refresh);
        return true;
    }

    // 使用清屏卡
    useClearProp() {
        if (this.props.clear <= 0) {
            console.log('清屏卡数量不足');
            return false;
        }

        this.props.clear--;

        // 清空所有网格
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 添加消失特效
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                }
                this.grid[row][col] = null;
            }
        }

        // 增加分数
        this.updateScore(800);
        this.addFloatingText('+800', this.canvas.width / 2, this.canvas.height / 2, '#FFD700');

        // 重新填充网格
        setTimeout(() => {
            this.refillGrid();
        }, 500);

        console.log('使用清屏卡，剩余数量:', this.props.clear);
        return true;
    }

    // 激活炸弹卡（需要用户选择位置）
    activateBombProp() {
        if (this.props.bomb <= 0) {
            console.log('炸弹卡数量不足');
            return false;
        }

        this.propUsing.type = 'bomb';
        this.propUsing.isActive = true;

        console.log('炸弹卡已激活，请选择爆炸位置');
        return true;
    }

    // 使用炸弹卡在指定位置
    useBombPropAt(row, col) {
        console.log(`尝试使用炸弹卡: 位置(${row}, ${col}), 激活状态: ${this.propUsing.isActive}, 类型: ${this.propUsing.type}, 数量: ${this.props.bomb}`);

        if (!this.propUsing.isActive || this.propUsing.type !== 'bomb') {
            console.log('炸弹卡使用失败: 道具未激活或类型不匹配');
            return false;
        }

        if (this.props.bomb <= 0) {
            console.log('炸弹卡使用失败: 数量不足');
            return false;
        }

        this.props.bomb--;
        this.propUsing.isActive = false;
        this.propUsing.type = null;

        console.log(`炸弹卡使用成功: 位置(${row}, ${col}), 剩余数量: ${this.props.bomb}`);

        // 5x5范围爆炸
        const eliminatedBlocks = this.eliminateArea(row, col, 5);

        // 增加分数
        this.updateScore(500);
        this.addFloatingText('+500', col * this.blockSize + this.gridStartX + this.blockSize/2,
                           row * this.blockSize + this.gridStartY + this.blockSize/2, '#FFD700');

        // 播放爆炸音效和特效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('bomb');
        }
        this.addExplosionEffect(col * this.blockSize + this.gridStartX + this.blockSize/2,
                              row * this.blockSize + this.gridStartY + this.blockSize/2, true);

        // 清除被消除的方块
        eliminatedBlocks.forEach(item => {
            this.grid[item.row][item.col] = null;
        });

        // 处理掉落
        setTimeout(() => {
            if (this.animator && this.animator.falling) {
                const removedPositions = eliminatedBlocks.map(item => ({ row: item.row, col: item.col }));
                this.animator.falling.processFalling(removedPositions);
            }
        }, 300);

        console.log('使用炸弹卡，剩余数量:', this.props.bomb);
        return true;
    }

    // 重新填充网格
    refillGrid() {
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (!this.grid[row][col]) {
                    const newBlock = this.createRandomBlock(row, col, this.gridStartX, this.gridStartY);
                    this.grid[row][col] = newBlock;
                }
            }
        }

        // 确保没有初始匹配
        this.removeInitialMatches();
    }
    
    // 初始化背景效果
    initBackgroundEffects() {
        try {
            if (typeof BackgroundUtils !== 'undefined') {
                this.backgroundStars = BackgroundUtils.createStars(15, this.canvas.width, this.canvas.height);
            } else {
                console.warn('BackgroundUtils未加载，使用简化背景');
                this.backgroundStars = null;
            }
        } catch (error) {
            console.warn('背景效果初始化失败:', error);
            this.backgroundStars = null;
        }
    }
    
    // 加载萌宠图片
    loadAnimalImages() {
        console.log('开始加载萌宠图片...');
        
        // 加载萌宠图片
        this.animalTypes.forEach(type => {
            const img = new Image();
            img.src = `images/animal/${type}.png`;
            img.onload = () => {
                console.log(`萌宠${type}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`萌宠${type}图片加载失败，使用默认颜色`);
            };
            this.animalImages[type] = img;
        });
        
        // 加载特殊方块图片
        const specialTypes = [
            { key: 'rocket', path: 'images/extra/rocket.png' },
            { key: 'bomb_extra', path: 'images/extra/bomb.png' }
        ];
        specialTypes.forEach(item => {
            const img = new Image();
            img.src = item.path;
            img.onload = () => {
                console.log(`特殊方块${item.key}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`特殊方块${item.key}图片加载失败，使用默认图标`);
            };
            this.propImages[item.key] = img;
        });

        // 加载道具图片
        const propTypes = ['refresh', 'bomb', 'clear'];
        propTypes.forEach(type => {
            const img = new Image();
            img.src = `images/prop/${type}.png`;
            img.onload = () => {
                console.log(`道具${type}图片加载完成`);
            };
            img.onerror = () => {
                console.warn(`道具${type}图片加载失败，使用默认图标`);
            };
            this.propImages[type] = img;
        });
    }
    
    // 检查匹配（增强版本）- 支持3连消、4连消、5连消
    checkForMatches() {
        const matches = [];
        const visited = new Set();
        const matchGroups = []; // 存储匹配组信息

        // 检查水平匹配
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX - 2; col++) {
                const block = this.grid[row][col];
                if (!block || block.blockType !== 'normal' || !block.type) {
                    console.log(`跳过方块 (${row},${col}): ${!block ? '空' : block.blockType !== 'normal' ? '非普通' : '无type'}`);
                    continue;
                }

                const type = block.type;
                const block1 = this.grid[row][col + 1];
                const block2 = this.grid[row][col + 2];

                if (block1?.type === type && block1?.blockType === 'normal' &&
                    block2?.type === type && block2?.blockType === 'normal') {

                    let count = 3;
                    let endCol = col + 2;

                    // 扩展匹配
                    while (endCol + 1 < this.gridSizeX) {
                        const nextBlock = this.grid[row][endCol + 1];
                        if (nextBlock?.type === type && nextBlock?.blockType === 'normal') {
                            count++;
                            endCol++;
                        } else {
                            break;
                        }
                    }

                    // 记录匹配组
                    const matchGroup = {
                        type: 'horizontal',
                        count: count,
                        blocks: []
                    };

                    // 添加匹配的方块
                    for (let i = 0; i < count; i++) {
                        const key = `${row}-${col + i}`;
                        if (!visited.has(key)) {
                            const matchBlock = { row, col: col + i, type, count, matchType: 'horizontal' };
                            matches.push(matchBlock);
                            matchGroup.blocks.push(matchBlock);
                            visited.add(key);
                        }
                    }

                    matchGroups.push(matchGroup);
                    console.log(`检测到水平${count}连消: 行${row}, 列${col}-${endCol}, 类型${type}`);
                    col = endCol;
                }
            }
        }

        // 检查垂直匹配
        for (let col = 0; col < this.gridSizeX; col++) {
            for (let row = 0; row < this.gridSizeY - 2; row++) {
                const block = this.grid[row][col];
                if (!block || block.blockType !== 'normal' || !block.type) {
                    console.log(`跳过方块 (${row},${col}): ${!block ? '空' : block.blockType !== 'normal' ? '非普通' : '无type'}`);
                    continue;
                }

                const type = block.type;
                const block1 = this.grid[row + 1][col];
                const block2 = this.grid[row + 2][col];

                if (block1?.type === type && block1?.blockType === 'normal' &&
                    block2?.type === type && block2?.blockType === 'normal') {

                    let count = 3;
                    let endRow = row + 2;

                    // 扩展匹配
                    while (endRow + 1 < this.gridSizeY) {
                        const nextBlock = this.grid[endRow + 1][col];
                        if (nextBlock?.type === type && nextBlock?.blockType === 'normal') {
                            count++;
                            endRow++;
                        } else {
                            break;
                        }
                    }

                    // 记录匹配组
                    const matchGroup = {
                        type: 'vertical',
                        count: count,
                        blocks: []
                    };

                    // 添加匹配的方块
                    for (let i = 0; i < count; i++) {
                        const key = `${row + i}-${col}`;
                        if (!visited.has(key)) {
                            const matchBlock = { row: row + i, col, type, count, matchType: 'vertical' };
                            matches.push(matchBlock);
                            matchGroup.blocks.push(matchBlock);
                            visited.add(key);
                        }
                    }

                    matchGroups.push(matchGroup);
                    console.log(`检测到垂直${count}连消: 列${col}, 行${row}-${endRow}, 类型${type}`);
                    row = endRow;
                }
            }
        }

        return { matches, matchGroups };
    }
    
    // 处理匹配消除 - 支持新计分规则和特殊方块生成
    processMatches(matchResult) {
        if (!matchResult || matchResult.matches.length === 0) {
            // 没有匹配时重置连击
            this.resetCombo();
            return { score: 0, specialBlocks: [] };
        }

        const { matches, matchGroups } = matchResult;
        let totalScore = 0;
        const specialBlocks = [];

        // 增加连击数
        this.comboCount++;
        this.maxCombo = Math.max(this.maxCombo, this.comboCount);

        // 计算连击倍率
        const comboIndex = Math.min(this.comboCount - 1, this.scoreRules.comboMultipliers.length - 1);
        this.comboMultiplier = this.scoreRules.comboMultipliers[comboIndex];

        // 播放消除音效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('so');
        }

        // 处理每个匹配组
        matchGroups.forEach((group, index) => {
            // 验证匹配组数据完整性
            if (!group || (!group.blocks && !group.length)) {
                console.error(`匹配组${index + 1}数据无效:`, group);
                return;
            }

            let baseScore;
            // 获取匹配数量，兼容不同的数据结构
            const count = group.count || group.length || (group.blocks ? group.blocks.length : 0);
            console.log(`处理匹配组${index + 1}: 类型${group.type}, 数量${count}, 结构:`, group);

            // 根据消除数量计算基础分数
            if (count === 3) {
                baseScore = this.scoreRules.match3;
            } else if (count === 4) {
                baseScore = this.scoreRules.match4;
                // 4连消生成火箭
                const blocks = group.blocks || [group];
                if (Array.isArray(blocks) && blocks.length > 0) {
                    const centerBlock = blocks[Math.floor(blocks.length / 2)];
                    specialBlocks.push({
                        row: centerBlock.row,
                        col: centerBlock.col,
                        type: 'rocket'
                    });
                    console.log(`4连消生成火箭: 位置(${centerBlock.row}, ${centerBlock.col})`);
                }
            } else if (count >= 5) {
                baseScore = this.scoreRules.match5;
                // 5连消生成炸弹
                const blocks = group.blocks || [group];
                if (Array.isArray(blocks) && blocks.length > 0) {
                    const centerBlock = blocks[Math.floor(blocks.length / 2)];
                    specialBlocks.push({
                        row: centerBlock.row,
                        col: centerBlock.col,
                        type: 'bomb'
                    });
                    console.log(`5连消生成炸弹: 位置(${centerBlock.row}, ${centerBlock.col})`);
                }
            } else {
                // 默认分数，防止undefined
                baseScore = this.scoreRules.match3;
                console.warn(`未知的匹配数量: ${count}, 使用默认分数`);
            }

            const finalScore = Math.floor(baseScore * this.comboMultiplier);
            totalScore += finalScore;
            console.log(`消除组得分: 基础${baseScore} x 倍率${this.comboMultiplier} = ${finalScore}, 累计${totalScore}`);

            // 为每个方块添加特效
            const blocks = group.blocks || [group]; // 兼容不同的数据结构
            if (Array.isArray(blocks)) {
                blocks.forEach(match => {
                    const block = this.grid[match.row][match.col];
                    if (block) {
                        // 标记为已匹配
                        block.isMatched = true;

                        // 添加粒子效果
                        this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    }
                });
            } else {
                console.error('group.blocks不是数组:', blocks, '完整group:', group);
            }

            // 添加浮动分数文字
            const blocks = group.blocks || [group];
            if (Array.isArray(blocks) && blocks.length > 0) {
                const centerBlock = blocks[Math.floor(blocks.length / 2)];
                const block = this.grid[centerBlock.row][centerBlock.col];
                if (block) {
                    this.addFloatingText(`+${baseScore}`, block.x + this.blockSize / 2, block.y + this.blockSize / 2, '#FFD700');
                }
            }
        });

        // 添加连击显示和音效
        if (this.comboCount > 1) {
            this.addComboText(this.comboCount, this.comboMultiplier);

            // 播放连击音效 - 只在特定连击数时播放一次
            if (this.gameManager.audioManager) {
                if (this.comboCount === 2) {
                    this.gameManager.audioManager.playSound('wa');
                    console.log('播放2连击音效: wa.mp3');
                } else if (this.comboCount === 5) {
                    this.gameManager.audioManager.playSound('good');
                    console.log('播放5连击音效: good.mp3');
                }
            }
        }

        // 检查是否还有可能的消除
        setTimeout(() => {
            this.checkForPossibleMoves();
        }, 500);

        return { score: totalScore, specialBlocks };
    }

    // 重置连击
    resetCombo() {
        this.comboCount = 0;
        this.comboMultiplier = 1.0;
        this.comboTimer = 0;
        console.log('连击重置');
    }

    // 检查是否还有可能的移动
    checkForPossibleMoves() {
        if (this.isGameOver || this.isLevelComplete || this.isAnimating) {
            return;
        }

        console.log('检查可能的移动...');

        // 检查所有相邻位置的交换是否能产生匹配
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (!block) continue;

                // 检查右边的交换
                if (col < this.gridSizeX - 1) {
                    const rightBlock = this.grid[row][col + 1];
                    if (rightBlock && this.canSwapAndMatch(row, col, row, col + 1)) {
                        console.log(`找到可能的移动: (${row},${col}) <-> (${row},${col + 1})`);
                        return; // 找到可能的移动，游戏继续
                    }
                }

                // 检查下边的交换
                if (row < this.gridSizeY - 1) {
                    const bottomBlock = this.grid[row + 1][col];
                    if (bottomBlock && this.canSwapAndMatch(row, col, row + 1, col)) {
                        console.log(`找到可能的移动: (${row},${col}) <-> (${row + 1},${col})`);
                        return; // 找到可能的移动，游戏继续
                    }
                }
            }
        }

        // 没有找到可能的移动，游戏失败
        console.log('没有找到可能的移动，游戏失败');
        this.triggerGameOver();
    }

    // 检查交换后是否能产生匹配
    canSwapAndMatch(row1, col1, row2, col2) {
        // 临时交换
        const temp = this.grid[row1][col1];
        this.grid[row1][col1] = this.grid[row2][col2];
        this.grid[row2][col2] = temp;

        // 检查是否有匹配
        const matchResult = this.checkForMatches();
        const hasMatch = matchResult && matchResult.matches.length > 0;

        // 交换回来
        this.grid[row2][col2] = this.grid[row1][col1];
        this.grid[row1][col1] = temp;

        return hasMatch;
    }

    // 触发游戏失败
    triggerGameOver() {
        this.isGameOver = true;

        // 播放失败音效
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound('lose');
        }

        // 显示失败对话框
        setTimeout(() => {
            this.showGameOverDialog();
        }, 500);

        console.log('游戏失败');
    }

    // 显示游戏失败对话框
    showGameOverDialog() {
        this.showGameOverDialog = true;
        console.log('显示游戏失败对话框');
    }

    // 复活游戏（触发刷新卡效果但不消耗刷新卡）
    reviveGame() {
        console.log('复活游戏');
        this.isGameOver = false;
        this.showGameOverDialog = false;

        // 触发刷新卡效果但不消耗数量
        this.shuffleGrid();

        // 确保没有初始匹配
        this.removeInitialMatches();

        console.log('游戏复活成功');
    }

    // 打乱网格（刷新卡效果）
    shuffleGrid() {
        const blocks = [];

        // 收集所有方块
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (this.grid[row][col]) {
                    blocks.push(this.grid[row][col]);
                }
            }
        }

        // 打乱数组
        for (let i = blocks.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [blocks[i], blocks[j]] = [blocks[j], blocks[i]];
        }

        // 重新分配到网格
        let blockIndex = 0;
        for (let row = 0; row < this.gridSizeY; row++) {
            for (let col = 0; col < this.gridSizeX; col++) {
                if (blockIndex < blocks.length) {
                    const block = blocks[blockIndex];
                    block.row = row;
                    block.col = col;
                    // 包含间距的位置计算
                    const spacing = this.getSpacing();
                    block.x = this.gridStartX + col * (this.blockSize + spacing);
                    block.y = this.gridStartY + row * (this.blockSize + spacing);
                    this.grid[row][col] = block;
                    blockIndex++;
                } else {
                    this.grid[row][col] = null;
                }
            }
        }

        console.log('网格已打乱');
    }

    // 处理格子交换 - 完全重写
    processSwap(block1, block2, row1, col1, row2, col2) {
        console.log(`交换处理: (${row1},${col1})[${block1.type}] ↔ (${row2},${col2})[${block2.type}]`);

        // 判断格子类型
        const isSpecial1 = block1.category === 'special';
        const isSpecial2 = block2.category === 'special';

        console.log(`格子分类: 格子1=${isSpecial1 ? '特殊' : '普通'}, 格子2=${isSpecial2 ? '特殊' : '普通'}`);

        // 情况1: 两个都是特殊格子
        if (isSpecial1 && isSpecial2) {
            return this.handleTwoSpecialBlocks(block1, block2, row1, col1, row2, col2);
        }

        // 情况2: 一个特殊格子 + 一个普通格子
        else if (isSpecial1 || isSpecial2) {
            return this.handleSpecialWithNormal(block1, block2, row1, col1, row2, col2);
        }

        // 情况3: 两个都是普通格子
        else {
            return this.handleTwoNormalBlocks(block1, block2, row1, col1, row2, col2);
        }
    }

    // 情况1: 处理两个特殊格子的交换
    handleTwoSpecialBlocks(block1, block2, row1, col1, row2, col2) {
        console.log(`情况1: 两个特殊格子交换 - ${block1.specialType} + ${block2.specialType}`);

        const type1 = block1.specialType;
        const type2 = block2.specialType;
        let eliminatedBlocks = [];
        let score = 0;

        // 火箭 + 火箭 = 十字消除
        if (type1 === 'rocket' && type2 === 'rocket') {
            console.log('火箭+火箭: 十字消除');
            eliminatedBlocks = this.eliminateCross(row1, col1);
            score = 200;
            this.playSound('shua');
        }

        // 火箭 + 炸弹 = 3列消除
        else if ((type1 === 'rocket' && type2 === 'bomb') || (type1 === 'bomb' && type2 === 'rocket')) {
            console.log('火箭+炸弹: 3列消除');
            const centerCol = Math.floor((col1 + col2) / 2);
            eliminatedBlocks = this.eliminateThreeColumns(centerCol);
            score = 300;
            this.playSound('bomb');
        }

        // 炸弹 + 炸弹 = 5x5爆炸
        else if (type1 === 'bomb' && type2 === 'bomb') {
            console.log('炸弹+炸弹: 5x5爆炸');
            const centerRow = Math.floor((row1 + row2) / 2);
            const centerCol = Math.floor((col1 + col2) / 2);
            eliminatedBlocks = this.eliminateArea(centerRow, centerCol, 5);
            score = 500;
            this.playSound('bomb');
            this.addExplosionEffect(centerCol, centerRow, true);
        }

        console.log(`两个特殊格子交换完成: 消除${eliminatedBlocks.length}个格子, 得分${score}`);
        return { score, eliminatedBlocks, success: true };
    }

    // 情况2: 处理特殊格子与普通格子的交换
    handleSpecialWithNormal(block1, block2, row1, col1, row2, col2) {
        console.log(`情况2: 特殊格子与普通格子交换`);

        // 确定哪个是特殊格子，哪个是普通格子
        const isSpecial1 = block1.category === 'special';
        const specialBlock = isSpecial1 ? block1 : block2;
        const normalBlock = isSpecial1 ? block2 : block1;
        const specialPos = isSpecial1 ? {row: row1, col: col1} : {row: row2, col: col2};
        const normalPos = isSpecial1 ? {row: row2, col: col2} : {row: row1, col: col1};

        console.log(`特殊格子: ${specialBlock.specialType} 在 (${specialPos.row},${specialPos.col})`);
        console.log(`普通格子: ${normalBlock.type} 在 (${normalPos.row},${normalPos.col})`);

        // 检查交换后普通格子是否能形成匹配
        if (this.checkSwapMatch(normalBlock, normalPos, specialPos)) {
            console.log('普通格子交换后能形成匹配，执行特殊效果');

            // 执行交换
            this.executeSwap(row1, col1, row2, col2);

            // 触发特殊格子效果
            return this.triggerSpecialEffect(specialBlock, normalPos);
        } else {
            console.log('普通格子交换后无法形成匹配，交换失败');
            return { score: 0, eliminatedBlocks: [], success: false };
        }
    }

    // 情况3: 处理两个普通格子的交换
    handleTwoNormalBlocks(block1, block2, row1, col1, row2, col2) {
        console.log(`情况3: 两个普通格子交换`);

        // 检查交换后是否能形成匹配
        const pos1 = {row: row1, col: col1};
        const pos2 = {row: row2, col: col2};

        const canMatch1 = this.checkSwapMatch(block1, pos1, pos2);
        const canMatch2 = this.checkSwapMatch(block2, pos2, pos1);

        if (canMatch1 || canMatch2) {
            console.log('普通格子交换后能形成匹配，执行交换');
            this.executeSwap(row1, col1, row2, col2);
            return { score: 0, eliminatedBlocks: [], success: true, normalSwap: true };
        } else {
            console.log('普通格子交换后无法形成匹配，交换失败');
            return { score: 0, eliminatedBlocks: [], success: false };
        }
    }

    // 消除整列
    eliminateColumn(col) {
        const eliminatedBlocks = [];
        for (let row = 0; row < this.gridSizeY; row++) {
            const block = this.grid[row][col];
            if (block) {
                eliminatedBlocks.push({ row, col, block });
                this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                // 实际清除网格中的格子
                this.grid[row][col] = null;
            }
        }
        console.log(`消除列${col}: 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }

    // 检查交换后是否能形成匹配
    checkSwapMatch(block, fromPos, toPos) {
        // 保存原始状态
        const originalBlock = this.grid[toPos.row][toPos.col];

        // 获取间距
        const spacing = this.getSpacing();

        // 临时执行交换 - 创建一个完整的格子副本
        const tempBlock = {
            ...block,
            row: toPos.row,
            col: toPos.col,
            x: this.gridStartX + toPos.col * (this.blockSize + spacing),
            y: this.gridStartY + toPos.row * (this.blockSize + spacing)
        };

        this.grid[toPos.row][toPos.col] = tempBlock;

        // 检查新位置是否有匹配
        const hasMatch = this.matcher.checkPositionForMatch(toPos.row, toPos.col);

        // 恢复原状
        this.grid[toPos.row][toPos.col] = originalBlock;

        console.log(`匹配检查: ${block.type} 移动到 (${toPos.row},${toPos.col}) → ${hasMatch ? '有匹配' : '无匹配'}`);

        return hasMatch;
    }

    // 执行格子交换
    executeSwap(row1, col1, row2, col2) {
        const block1 = this.grid[row1][col1];
        const block2 = this.grid[row2][col2];

        console.log(`执行交换前: (${row1},${col1})[${block1.type}] ↔ (${row2},${col2})[${block2.type}]`);

        // 获取间距
        const spacing = this.getSpacing();

        // 更新block1的位置信息（移动到位置2）
        block1.row = row2;
        block1.col = col2;
        block1.x = this.gridStartX + col2 * (this.blockSize + spacing);
        block1.y = this.gridStartY + row2 * (this.blockSize + spacing);

        // 更新block2的位置信息（移动到位置1）
        block2.row = row1;
        block2.col = col1;
        block2.x = this.gridStartX + col1 * (this.blockSize + spacing);
        block2.y = this.gridStartY + row1 * (this.blockSize + spacing);

        // 交换网格中的位置
        this.grid[row1][col1] = block2;
        this.grid[row2][col2] = block1;

        console.log(`执行交换后: (${row1},${col1})[${block2.type}] ↔ (${row2},${col2})[${block1.type}]`);
    }

    // 触发特殊格子效果
    triggerSpecialEffect(specialBlock, targetPos) {
        const specialType = specialBlock.specialType;
        let eliminatedBlocks = [];
        let score = 0;

        console.log(`触发特殊效果: ${specialType} 在位置 (${targetPos.row},${targetPos.col})`);

        switch (specialType) {
            case 'rocket':
                // 火箭：消除整列
                eliminatedBlocks = this.eliminateColumn(targetPos.col);
                score = 100;
                this.playSound('shua');
                break;

            case 'bomb':
                // 炸弹：消除3x3区域
                eliminatedBlocks = this.eliminateArea(targetPos.row, targetPos.col, 3);
                score = 150;
                this.playSound('bomb');
                this.addExplosionEffect(targetPos.col, targetPos.row);
                break;
        }

        console.log(`特殊效果完成: 消除${eliminatedBlocks.length}个格子, 得分${score}`);
        return { score, eliminatedBlocks, success: true };
    }

    // 播放音效
    playSound(soundName) {
        if (this.gameManager.audioManager) {
            this.gameManager.audioManager.playSound(soundName);
        }
    }

    // 添加爆炸效果
    addExplosionEffect(col, row, large = false) {
        const x = this.gridStartX + col * (this.blockSize + this.getSpacing()) + this.blockSize / 2;
        const y = this.gridStartY + row * (this.blockSize + this.getSpacing()) + this.blockSize / 2;

        // 这里可以添加爆炸特效的实现
        console.log(`爆炸效果: (${x}, ${y}) ${large ? '大爆炸' : '小爆炸'}`);
    }

    // 消除整行
    eliminateRow(row) {
        const eliminatedBlocks = [];
        for (let col = 0; col < this.gridSizeX; col++) {
            const block = this.grid[row][col];
            if (block) {
                eliminatedBlocks.push({ row, col, block });
                this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                this.grid[row][col] = null;
            }
        }
        console.log(`消除行${row}: 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }

    // 消除十字形（一行一列）
    eliminateCross(centerRow, centerCol) {
        const eliminatedBlocks = [];

        // 消除整行
        eliminatedBlocks.push(...this.eliminateRow(centerRow));

        // 消除整列（避免重复消除中心点）
        for (let row = 0; row < this.gridSizeY; row++) {
            if (row !== centerRow) {
                const block = this.grid[row][centerCol];
                if (block) {
                    eliminatedBlocks.push({ row, col: centerCol, block });
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    this.grid[row][centerCol] = null;
                }
            }
        }

        console.log(`消除十字形(${centerRow},${centerCol}): 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }

    // 消除3列（中心列及其左右各一列）
    eliminateThreeColumns(centerCol) {
        const eliminatedBlocks = [];

        for (let c = Math.max(0, centerCol - 1); c <= Math.min(this.gridSizeX - 1, centerCol + 1); c++) {
            eliminatedBlocks.push(...this.eliminateColumn(c));
        }

        console.log(`消除3列(中心列${centerCol}): 清除了${eliminatedBlocks.length}个格子`);
        return eliminatedBlocks;
    }

    // 消除十字形区域
    eliminateCross(centerRow, centerCol) {
        const eliminatedBlocks = [];

        // 消除整行
        for (let col = 0; col < this.gridSizeX; col++) {
            const block = this.grid[centerRow][col];
            if (block) {
                eliminatedBlocks.push({ row: centerRow, col, block });
                this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
            }
        }

        // 消除整列
        for (let row = 0; row < this.gridSizeY; row++) {
            if (row !== centerRow) { // 避免重复消除中心点
                const block = this.grid[row][centerCol];
                if (block) {
                    eliminatedBlocks.push({ row, col: centerCol, block });
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                }
            }
        }

        return eliminatedBlocks;
    }

    // 消除矩形区域
    eliminateArea(centerRow, centerCol, size) {
        const eliminatedBlocks = [];
        const radius = Math.floor(size / 2);

        for (let row = Math.max(0, centerRow - radius); row <= Math.min(this.gridSizeY - 1, centerRow + radius); row++) {
            for (let col = Math.max(0, centerCol - radius); col <= Math.min(this.gridSizeX - 1, centerCol + radius); col++) {
                const block = this.grid[row][col];
                if (block) {
                    eliminatedBlocks.push({ row, col, block });
                    // 标记方块为已移除
                    block.isMatched = true;
                    // 添加爆炸特效
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                    console.log(`炸弹消除方块: (${row}, ${col})`);
                }
            }
        }

        return eliminatedBlocks;
    }

    // 消除三列
    eliminateThreeColumns(centerCol) {
        const eliminatedBlocks = [];
        const startCol = Math.max(0, centerCol - 1);
        const endCol = Math.min(this.gridSizeX - 1, centerCol + 1);

        for (let col = startCol; col <= endCol; col++) {
            for (let row = 0; row < this.gridSizeY; row++) {
                const block = this.grid[row][col];
                if (block) {
                    eliminatedBlocks.push({ row, col, block });
                    this.addParticleEffect(block.x + this.blockSize / 2, block.y + this.blockSize / 2, block.color);
                }
            }
        }

        return eliminatedBlocks;
    }

    // 添加爆炸效果
    addExplosionEffect(x, y, isBig = false) {
        const particleCount = isBig ? 20 : 12;
        const colors = ['#FF4500', '#FF6347', '#FFD700', '#FF69B4', '#00CED1'];

        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * (isBig ? 15 : 10),
                vy: (Math.random() - 0.5) * (isBig ? 15 : 10),
                size: Math.random() * (isBig ? 8 : 6) + (isBig ? 4 : 2),
                color: colors[Math.floor(Math.random() * colors.length)],
                life: 1,
                decay: 0.015
            });
        }

        // 添加爆炸闪烁效果
        for (let i = 0; i < (isBig ? 8 : 5); i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * (isBig ? 40 : 25),
                y: y + (Math.random() - 0.5) * (isBig ? 40 : 25),
                size: Math.random() * (isBig ? 12 : 8) + (isBig ? 6 : 4),
                life: 1,
                decay: 0.02,
                twinkle: Math.random() * Math.PI * 2
            });
        }
    }
    
    // 添加粒子效果
    addParticleEffect(x, y, color) {
        for (let i = 0; i < 8; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                size: Math.random() * 4 + 2,
                color: color,
                life: 1,
                decay: 0.02
            });
        }
        
        // 添加闪烁效果
        for (let i = 0; i < 3; i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * 20,
                y: y + (Math.random() - 0.5) * 20,
                size: Math.random() * 6 + 4,
                life: 1,
                decay: 0.03,
                twinkle: 0
            });
        }
    }
    
    // 添加浮动文字
    addFloatingText(text, x, y, color) {
        this.floatingTexts.push({
            text: text,
            x: x,
            y: y,
            vx: 0,
            vy: -2,
            color: color,
            life: 1,
            decay: 0.015,
            scale: 1
        });
    }

    // 添加连击文字（更大更醒目）
    addComboText(comboCount, multiplier) {
        // 根据连击数确定大小和颜色
        let scale, color, duration;

        if (comboCount >= 6) {
            scale = 2.8;
            color = '#FF0000'; // 红色 - 6次以上
            duration = 1.2;
        } else if (comboCount >= 3) {
            scale = 2.3;
            color = '#FF8C00'; // 橘黄色 - 3-5次
            duration = 1.0;
        } else {
            scale = 2.0;
            color = '#32CD32'; // 绿色 - 3次以下
            duration = 0.8;
        }

        this.floatingTexts.push({
            text: `${comboCount}连击! x${multiplier}`,
            x: this.canvas.width / 2,
            y: this.canvas.height / 2 - 50,
            vx: 0,
            vy: -1,
            color: color,
            life: 1,
            decay: 0.015, // 缩短动画时间
            scale: scale,
            isCombo: true, // 标记为连击文字
            pulsePhase: 0, // 脉动相位
            duration: duration
        });

        console.log(`显示连击文字: ${comboCount}连击! x${multiplier}`);
    }
    
    // 重新开始游戏
    restartGame() {
        this.resetScore();
        this.isGameOver = false;
        this.isLevelComplete = false;
        this.isAnimating = false;
        this.showExitDialog = false;
        this.isBombCardSelecting = false;
        
        // 清空特效
        this.particles = [];
        this.sparkles = [];
        this.floatingTexts = [];
        this.animations = [];
        
        this.props = {
            refresh: 3,
            bomb: 2,
            clear: 1
        };
        
        this.initGrid();
        console.log('游戏重新开始');
    }
    
    // 检查游戏状态
    checkGameStatus() {
        // 检查是否达到目标分数，但要等所有连消完毕
        if (this.score >= this.targetScore && !this.isAnimating) {
            if (!this.isLevelComplete) {
                this.isLevelComplete = true;
                console.log('恭喜过关！');

                // 播放胜利音效
                if (this.gameManager.audioManager) {
                    this.gameManager.audioManager.playSound('win');
                }

                // 添加庆祝效果
                this.addCelebrationEffect();

                // 延迟显示通关提示
                setTimeout(() => {
                    this.showLevelCompleteDialog();
                }, 1000);
            }
        }
    }

    // 显示通关对话框
    showLevelCompleteDialog() {
        // 这里可以显示通关对话框，提示用户进入下一关
        console.log('显示通关对话框');
        // 可以设置一个标志位来显示通关UI
        this.showLevelCompleteDialog = true;
    }
    
    // 添加庆祝效果
    addCelebrationEffect() {
        // 添加大量彩色粒子
        for (let i = 0; i < 50; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 10,
                vy: (Math.random() - 0.5) * 10,
                size: Math.random() * 8 + 4,
                color: ['#FFD700', '#FF69B4', '#00CED1', '#98FB98', '#DDA0DD'][Math.floor(Math.random() * 5)],
                life: 1,
                decay: 0.01
            });
        }
        
        // 添加闪烁星星
        for (let i = 0; i < 20; i++) {
            this.sparkles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 10 + 5,
                life: 1,
                decay: 0.008,
                twinkle: Math.random() * Math.PI * 2
            });
        }
    }
    
    // 更新游戏状态
    update() {
        this.animationTime += 0.016; // 60fps
        
        // 更新背景效果
        try {
            if (this.backgroundStars && typeof BackgroundUtils !== 'undefined') {
                BackgroundUtils.updateStars(this.backgroundStars);
            }
        } catch (error) {
            console.warn('背景更新失败:', error);
        }
        
        // 更新动画控制器
        if (this.animator) {
            this.animator.update();
            this.isAnimating = this.animator.hasActiveAnimations();
        }
        
        // 更新粒子效果
        this.updateParticles();
        
        // 更新闪烁效果
        this.updateSparkles();
        
        // 更新浮动文字
        this.updateFloatingTexts();
        
        // 更新方块动画
        this.updateBlockAnimations();
        
        // 更新游戏状态
        if (this.animator) {
            this.animator.update();
            this.isAnimating = this.animator.hasActiveAnimations();
        }
    }
    
    // 更新粒子效果
    updateParticles() {
        this.particles = this.particles.filter(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;
            particle.vx *= 0.98;
            particle.vy *= 0.98;
            return particle.life > 0;
        });
    }
    
    // 更新闪烁效果
    updateSparkles() {
        this.sparkles = this.sparkles.filter(sparkle => {
            sparkle.life -= sparkle.decay;
            sparkle.twinkle += 0.2;
            return sparkle.life > 0;
        });
    }
    
    // 更新浮动文字
    updateFloatingTexts() {
        this.floatingTexts = this.floatingTexts.filter(text => {
            text.x += text.vx;
            text.y += text.vy;
            text.life -= text.decay;
            text.scale = Math.max(0.5, text.scale - 0.01);
            return text.life > 0;
        });
    }
    
    // 更新方块动画（简化版本，移除浮动效果）
    updateBlockAnimations() {
        if (!this.grid || !Array.isArray(this.grid)) return;

        for (let row = 0; row < this.gridSizeY; row++) {
            if (!this.grid[row] || !Array.isArray(this.grid[row])) continue;

            for (let col = 0; col < this.gridSizeX; col++) {
                const block = this.grid[row][col];
                if (block) {
                    // 选中状态的特殊效果（保持静态）
                    if (block.isSelected) {
                        block.glowIntensity = Math.min(1, block.glowIntensity + 0.1);
                    } else {
                        block.glowIntensity = Math.max(0, block.glowIntensity - 0.05);
                    }
                    
                    // 匹配状态的消失动画
                    if (block.isMatched) {
                        block.alpha = Math.max(0, block.alpha - 0.1);
                        block.scale = Math.max(0, block.scale - 0.05);
                        block.rotation += 0.2;
                    } else {
                        // 保持默认状态
                        block.scale = 1;
                        block.rotation = 0;
                    }
                }
            }
        }
    }
    
    // 创建粒子效果
    createParticles(x, y, color, count = 8) {
        for (let i = 0; i < count; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                size: Math.random() * 4 + 2,
                color: color,
                life: 1,
                decay: 0.02
            });
        }
    }
    
    // 创建闪烁效果
    createSparkles(x, y, count = 3) {
        for (let i = 0; i < count; i++) {
            this.sparkles.push({
                x: x + (Math.random() - 0.5) * 20,
                y: y + (Math.random() - 0.5) * 20,
                size: Math.random() * 6 + 4,
                life: 1,
                decay: 0.03,
                twinkle: 0
            });
        }
    }
    
    // 创建浮动文字
    createFloatingText(x, y, text, color) {
        this.floatingTexts.push({
            text: text,
            x: x,
            y: y,
            vx: 0,
            vy: -2,
            color: color,
            life: 1,
            decay: 0.015,
            scale: 1
        });
    }
    
    // 网格起始位置（供事件处理使用）
    get gridStartX() {
        if (typeof CONFIG_UTILS !== 'undefined') {
            const gridConfig = CONFIG_UTILS.getGridConfig(this.canvas.width, this.canvas.height);
            return gridConfig.startX;
        } else {
            return (this.canvas.width - this.gridSizeX * this.blockSize) / 2;
        }
    }
    
    get gridStartY() {
        if (typeof CONFIG_UTILS !== 'undefined') {
            const gridConfig = CONFIG_UTILS.getGridConfig(this.canvas.width, this.canvas.height);
            return gridConfig.startY;
        } else {
            const config = GamePageCore.getConfig();
            return config.GRID ? config.GRID.START_Y : 280;
        }
    }

    // 静态方法：获取布局配置
    static getLayout() {
        const config = GamePageCore.getConfig();
        return config.LAYOUT || {};
    }
    
    // 静态方法：更新网格Y坐标
    static setGridStartY(y) {
        // 注意：这个方法现在不能直接修改配置，因为配置是只读的
        console.warn('setGridStartY: 配置现在是只读的，请在config.js中修改');
    }

    // 静态方法：获取网格起始X坐标（80%宽度居中）
    static getGridStartX(canvasWidth, canvasHeight) {
        if (typeof CONFIG_UTILS !== 'undefined') {
            const gridConfig = CONFIG_UTILS.getGridConfig(canvasWidth, canvasHeight);
            return gridConfig.startX;
        } else {
            // 回退到默认计算
            const config = GamePageCore.getConfig();
            const gridWidth = (config.GRID ? config.GRID.SIZE_X : 8) * 45;
            return (canvasWidth - gridWidth) / 2;
        }
    }

    // 静态方法：获取网格起始Y坐标
    static getGridStartY(canvasWidth, canvasHeight) {
        if (typeof CONFIG_UTILS !== 'undefined') {
            const gridConfig = CONFIG_UTILS.getGridConfig(canvasWidth, canvasHeight);
            return gridConfig.startY;
        } else {
            // 回退到默认值
            return 280;
        }
    }
    
    // 初始化方法
    init() {
        console.log('GamePageCore初始化');
        this.initGrid();
        this.initBackgroundEffects();
        this.loadAnimalImages();
        this.initAnimator();
    }
    
    // 初始化动画控制器
    initAnimator() {
        try {
            if (typeof GamePageAnimator !== 'undefined') {
                this.animator = new GamePageAnimator(this);
                this.animator.init();
                console.log('动画控制器初始化成功');
            } else {
                console.warn('GamePageAnimator未加载，使用简化版本');
                this.animator = null;
            }
        } catch (error) {
            console.error('动画控制器初始化失败:', error);
            this.animator = null;
        }
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GamePageCore;
} else {
    window.GamePageCore = GamePageCore;
}