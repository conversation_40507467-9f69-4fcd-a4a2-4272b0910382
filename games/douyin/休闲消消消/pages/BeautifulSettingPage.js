/**
 * 美化版设置页面模块
 * 负责音量控制和游戏设置功能
 */

// 导入背景工具类
let BackgroundUtils, BackgroundRenderer;
try {
    if (typeof require !== 'undefined') {
        const bgUtils = require('../utils/BackgroundUtils.js');
        BackgroundUtils = bgUtils.BackgroundUtils;
        BackgroundRenderer = bgUtils.BackgroundRenderer;
    } else {
        BackgroundUtils = window.BackgroundUtils;
        BackgroundRenderer = window.BackgroundRenderer;
    }
} catch (error) {
    console.warn('BackgroundUtils加载失败，使用简化版本');
}

class BeautifulSettingPage {
    constructor(gameManager) {
        this.gameManager = gameManager;
        this.canvas = gameManager.canvas;
        this.ctx = gameManager.ctx;
        this.buttons = [];
        this.sliders = [];
        this.animationTime = 0;
        this.backgroundStars = null;
        this.isDragging = false;
        this.dragSlider = null;
        
        this.init();
    }

    init() {
        console.log('初始化美化版设置页面');
        this.initBackground();
        this.initButtons();
        this.initSliders();
        this.bindEvents();
    }

    initBackground() {
        if (BackgroundUtils) {
            this.backgroundStars = BackgroundUtils.createStars(20, this.canvas.width, this.canvas.height);
        }
    }

    initButtons() {
        const centerX = this.canvas.width / 2;
        
        this.buttons = [
            {
                id: 'back',
                text: '返回',
                x: 20,
                y: 70,
                width: 100,
                height: 32,
                action: () => this.goBack()
            },
            {
                id: 'mute',
                text: this.gameManager.gameData.settings.muteMode ? '静音模式：开' : '静音模式：关',
                x: centerX - 120,
                y: 350,
                width: 240,
                height: 50,
                isActive: this.gameManager.gameData.settings.muteMode,
                action: () => this.toggleMuteMode()
            }
        ];
    }

    initSliders() {
        const centerX = this.canvas.width / 2;
        
        this.sliders = [
            {
                id: 'bgmVolume',
                label: '背景音乐音量',
                icon: '🎵',
                x: centerX - 150,
                y: 470,
                width: 300,
                height: 8,
                value: this.gameManager.gameData.settings.bgmVolume,
                onChange: (value) => this.updateBgmVolume(value)
            },
            {
                id: 'effectVolume',
                label: '音效音量',
                icon: '🔊',
                x: centerX - 150,
                y: 570,
                width: 300,
                height: 8,
                value: this.gameManager.gameData.settings.effectVolume,
                onChange: (value) => this.updateEffectVolume(value)
            }
        ];
    }

    updateBgmVolume(value) {
        this.gameManager.gameData.settings.bgmVolume = value;
        this.gameManager.saveGameData();
        console.log(`背景音乐音量调整为: ${Math.round(value * 100)}%`);
    }

    updateEffectVolume(value) {
        this.gameManager.gameData.settings.effectVolume = value;
        this.gameManager.saveGameData();
        console.log(`音效音量调整为: ${Math.round(value * 100)}%`);
    }

    toggleMuteMode() {
        this.gameManager.gameData.settings.muteMode = !this.gameManager.gameData.settings.muteMode;
        const muteButton = this.buttons.find(btn => btn.id === 'mute');
        if (muteButton) {
            muteButton.text = this.gameManager.gameData.settings.muteMode ? '静音模式：开' : '静音模式：关';
            muteButton.isActive = this.gameManager.gameData.settings.muteMode;
        }
        this.gameManager.saveGameData();
        console.log(`静音模式: ${this.gameManager.gameData.settings.muteMode ? '开启' : '关闭'}`);
    }

    goBack() {
        console.log('返回主页面');
        this.gameManager.switchToPage('main');
    }

    bindEvents() {
        this.touchStartHandler = (res) => {
            if (res.touches && res.touches.length > 0) {
                const touch = res.touches[0];
                this.handleTouchStart(touch.clientX, touch.clientY);
            }
        };
        
        this.touchMoveHandler = (res) => {
            if (res.touches && res.touches.length > 0) {
                const touch = res.touches[0];
                this.handleTouchMove(touch.clientX, touch.clientY);
            }
        };
        
        this.touchEndHandler = () => {
            this.handleTouchEnd();
        };
        
        if (typeof tt !== 'undefined') {
            tt.onTouchStart(this.touchStartHandler);
            tt.onTouchMove(this.touchMoveHandler);
            tt.onTouchEnd(this.touchEndHandler);
        }
    }

    handleTouchStart(x, y) {
        // 处理按钮点击
        for (let button of this.buttons) {
            if (this.isPointInButton(x, y, button)) {
                console.log(`点击了按钮: ${button.text}`);
                button.action();
                return;
            }
        }
        
        // 处理滑块拖拽开始
        for (let slider of this.sliders) {
            if (this.isPointInSlider(x, y, slider)) {
                this.isDragging = true;
                this.dragSlider = slider;
                this.updateSliderValue(x, slider);
                return;
            }
        }
    }

    handleTouchMove(x, y) {
        if (this.isDragging && this.dragSlider) {
            this.updateSliderValue(x, this.dragSlider);
        }
    }

    handleTouchEnd() {
        this.isDragging = false;
        this.dragSlider = null;
    }

    updateSliderValue(x, slider) {
        const newValue = Math.max(0, Math.min(1, (x - slider.x) / slider.width));
        slider.value = newValue;
        slider.onChange(newValue);
    }

    isPointInButton(x, y, button) {
        return x >= button.x && x <= button.x + button.width && 
               y >= button.y && y <= button.y + button.height;
    }

    isPointInSlider(x, y, slider) {
        return x >= slider.x - 20 && x <= slider.x + slider.width + 20 && 
               y >= slider.y - 20 && y <= slider.y + slider.height + 20;
    }

    update() {
        this.animationTime += 0.02;
        
        // 更新背景星星
        if (BackgroundUtils && this.backgroundStars) {
            BackgroundUtils.updateStars(this.backgroundStars);
        }
    }

    render() {
        // 绘制背景
        this.drawBackground();
        
        const centerX = this.canvas.width / 2;
        
        // 绘制标题
        this.drawTitle(centerX);
        
        // 绘制设置卡片
        this.drawSettingCard();
        
        // 绘制返回按钮
        const backButton = this.buttons.find(btn => btn.id === 'back');
        if (backButton) {
            this.renderBackButton(backButton.x, backButton.y, backButton.width, backButton.height, backButton.text);
        }
    }

    drawBackground() {
        if (BackgroundUtils && this.backgroundStars) {
            BackgroundUtils.drawCompleteBackground(
                this.ctx,
                this.canvas.width,
                this.canvas.height,
                this.backgroundStars,
                this.animationTime
            );
        } else {
            // 简化背景
            const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
    }

    drawTitle(centerX) {
        this.ctx.save();
        
        // 添加标题阴影效果
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 使用渐变色标题
        const gradient = this.ctx.createLinearGradient(centerX - 80, 70, centerX + 80, 100);
        gradient.addColorStop(0, '#FFD700');
        gradient.addColorStop(1, '#FF8C00');
        
        this.ctx.fillStyle = gradient;
        this.ctx.font = 'bold 36px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('游戏设置', centerX, 200);
        
        // 添加装饰性下划线
        this.ctx.beginPath();
        this.ctx.moveTo(centerX - 80, 210);
        this.ctx.lineTo(centerX + 80, 210);
        this.ctx.lineWidth = 3;
        this.ctx.strokeStyle = '#FFD700';
        this.ctx.stroke();
        
        this.ctx.restore();
    }

    drawSettingCard() {
        const cardY = 310;
        const cardHeight = 350;
        
        this.ctx.save();
        
        // 添加卡片阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        this.ctx.shadowBlur = 15;
        this.ctx.shadowOffsetX = 5;
        this.ctx.shadowOffsetY = 5;
        
        // 使用渐变背景
        const gradient = this.ctx.createLinearGradient(0, cardY, 0, cardY + cardHeight);
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.95)');
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0.85)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(40, cardY, this.canvas.width - 80, cardHeight, 20);
        this.ctx.fill();
        
        // 添加卡片边框
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.6)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.restore();
        
        // 绘制静音模式按钮
        this.drawMuteButton();
        
        // 绘制音量滑块
        this.drawSliders();
    }

    drawMuteButton() {
        const button = this.buttons.find(btn => btn.id === 'mute');
        if (!button) return;
        
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 8;
        this.ctx.shadowOffsetX = 3;
        this.ctx.shadowOffsetY = 3;
        
        // 根据状态选择颜色
        const gradient = this.ctx.createLinearGradient(
            button.x, button.y, 
            button.x, button.y + button.height
        );
        
        if (button.isActive) {
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#388E3C');
        } else {
            gradient.addColorStop(0, '#9E9E9E');
            gradient.addColorStop(1, '#616161');
        }
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height, 15);
        this.ctx.fill();
        
        // 添加光泽效果
        const glossGradient = this.ctx.createLinearGradient(
            button.x, button.y, 
            button.x, button.y + button.height / 2
        );
        glossGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
        glossGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
        this.ctx.fillStyle = glossGradient;
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height / 2, 15);
        this.ctx.fill();
        
        // 绘制按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.roundRect(button.x, button.y, button.width, button.height, 15);
        this.ctx.stroke();
        
        // 绘制按钮文字
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        // 添加文字阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
        this.ctx.shadowBlur = 3;
        this.ctx.shadowOffsetX = 1;
        this.ctx.shadowOffsetY = 1;
        
        this.ctx.fillText(button.text, button.x + button.width / 2, button.y + button.height / 2);
        
        this.ctx.restore();
    }

    drawSliders() {
        for (let slider of this.sliders) {
            this.ctx.save();
            
            // 绘制图标和标签
            this.ctx.fillStyle = '#333333';
            this.ctx.font = 'bold 18px Arial, "Microsoft YaHei"';
            this.ctx.textAlign = 'left';
            this.ctx.fillText(`${slider.icon} ${slider.label}`, slider.x, slider.y - 15);
            
            // 绘制滑块轨道背景
            this.ctx.fillStyle = '#E0E0E0';
            this.ctx.beginPath();
            this.ctx.roundRect(slider.x, slider.y, slider.width, slider.height, slider.height / 2);
            this.ctx.fill();
            
            // 绘制滑块进度
            const progressWidth = slider.width * slider.value;
            if (progressWidth > 0) {
                const progressGradient = this.ctx.createLinearGradient(
                    slider.x, slider.y, 
                    slider.x + progressWidth, slider.y
                );
                progressGradient.addColorStop(0, '#FF9800');
                progressGradient.addColorStop(1, '#F57C00');
                
                this.ctx.fillStyle = progressGradient;
                this.ctx.beginPath();
                this.ctx.roundRect(slider.x, slider.y, progressWidth, slider.height, slider.height / 2);
                this.ctx.fill();
            }
            
            // 绘制滑块手柄
            const handleX = slider.x + slider.width * slider.value;
            const handleRadius = 12;
            
            // 手柄阴影
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            this.ctx.shadowBlur = 6;
            this.ctx.shadowOffsetX = 2;
            this.ctx.shadowOffsetY = 2;
            
            // 手柄背景
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.beginPath();
            this.ctx.arc(handleX, slider.y + slider.height / 2, handleRadius, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 手柄边框
            this.ctx.shadowColor = 'transparent';
            this.ctx.strokeStyle = '#FF9800';
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.arc(handleX, slider.y + slider.height / 2, handleRadius, 0, Math.PI * 2);
            this.ctx.stroke();
            
            // 绘制数值
            this.ctx.fillStyle = '#666666';
            this.ctx.font = 'bold 16px Arial';
            this.ctx.textAlign = 'right';
            this.ctx.fillText(`${Math.round(slider.value * 100)}%`, slider.x + slider.width + 50, slider.y + slider.height / 2 + 5);
            
            this.ctx.restore();
        }
    }

    // 渲染返回按钮
    renderBackButton(x, y, width, height, text) {
        this.ctx.save();
        
        // 按钮阴影
        this.ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
        this.ctx.shadowBlur = 6;
        this.ctx.shadowOffsetX = 2;
        this.ctx.shadowOffsetY = 2;
        
        // 半透明白色背景
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.beginPath();
        this.ctx.roundRect(x, y, width, height, 15);
        this.ctx.fill();
        
        // 按钮边框
        this.ctx.shadowColor = 'transparent';
        this.ctx.strokeStyle = 'rgba(255, 140, 0, 0.8)';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        
        // 文字
        this.ctx.fillStyle = '#333333';
        this.ctx.font = 'bold 16px Arial, "Microsoft YaHei"';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        
        const textX = Math.round(x + width / 2);
        const textY = Math.round(y + height / 2);
        this.ctx.fillText(text, textX, textY);
        
        this.ctx.restore();
    }

    destroy() {
        console.log('销毁美化版设置页面');
        
        if (typeof tt !== 'undefined') {
            if (this.touchStartHandler) tt.offTouchStart(this.touchStartHandler);
            if (this.touchMoveHandler) tt.offTouchMove(this.touchMoveHandler);
            if (this.touchEndHandler) tt.offTouchEnd(this.touchEndHandler);
        }
        
        this.buttons = [];
        this.sliders = [];
        this.backgroundStars = null;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BeautifulSettingPage;
} else {
    window.BeautifulSettingPage = BeautifulSettingPage;
}
